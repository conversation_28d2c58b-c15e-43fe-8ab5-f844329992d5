package main

import (
	"context"
	"net/http"
	"time"

	"github.com/danielgtaylor/huma/v2"
	"github.com/jackc/pgx/v5/pgtype"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/activitylogs"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/converters"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/db"
)

func addNodeTypeEmailAddressRoutes(api huma.API, a *application) {
	// Get Node Type Email Address
	type GetNodeTypeEmailAddressInput struct {
		NodeID              string `path:"nodeID" format:"uuid" doc:"Node ID" example:"962d5baa-6d73-4caa-b343-af32a3819805"`
		IncludeActivityLogs bool   `query:"activity_logs" format:"bool" doc:"Whether to include activity logs (optional). Example: true or false." default:"true"`
	}

	type NodeEmailAddress struct {
		EmailAddress string `json:"email_address" format:"email" doc:"Email address"`
		NodeID       string `json:"node_id" format:"uuid" doc:"Node ID"`
	}

	type GetNodeTypeEmailAddressOutput struct {
		Body struct {
			Node         NodeEmailAddress `json:"node"`
			ActivityLogs []ActivityLog    `json:"activity_logs,omitempty" doc:"Activity Logs"`
		}
	}

	huma.Register(api, huma.Operation{
		OperationID:   "get-nodes-email-address",
		Method:        http.MethodGet,
		Path:          "/nodes/email_address/{nodeID}",
		Security:      []map[string][]string{{"microsoft_entra_auth": {"scope1"}}},
		Summary:       "Get an Email Address Node",
		Tags:          []string{"Nodes, Email Address"},
		DefaultStatus: http.StatusOK,
	}, func(ctx context.Context, i *GetNodeTypeEmailAddressInput) (*GetNodeTypeEmailAddressOutput, error) {
		userID, ok := ctx.Value("userID").(string)
		if !ok {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}
		resp := &GetNodeTypeEmailAddressOutput{}
		nodeIDPgType, err := converters.StringToPgTypeUUID(i.NodeID)
		if err != nil {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}
		userIDPgType, err := converters.StringToPgTypeUUID(userID)
		if err != nil {
			return nil, err
		}

		node, err := a.queries.GetNodeTypeEmailAddress(context.Background(), db.GetNodeTypeEmailAddressParams{
			NodeID: *nodeIDPgType,
			ID:     *userIDPgType,
		})
		if err != nil {
			a.logger.Error("Error getting Node Email Address", "user_id", userID, "nodeID", i.NodeID, "error", err.Error())
			return nil, huma.Error404NotFound("Node not found")
		}

		resp.Body.Node.EmailAddress = node.EmailAddress
		resp.Body.Node.NodeID = i.NodeID

		if i.IncludeActivityLogs {
			activityLogs := make([]ActivityLog, 0)
			activityLogsDB, err := a.queries.GetNodeActivityLogs(context.Background(), *nodeIDPgType)
			if err != nil {
				a.logger.Error("Error getting activity logs", "user_id", userID, "nodeID", i.NodeID, "error", err.Error())
				return nil, huma.Error404NotFound("Activity logs not found")
			}
			for _, activityLogDB := range activityLogsDB {
				activityLogs = append(activityLogs, ActivityLog{
					Message:   activityLogDB.Message,
					Type:      activityLogDB.Type,
					Username:  activityLogDB.Username,
					CreatedAt: activityLogDB.CreatedAt.Time,
				})
			}
			resp.Body.ActivityLogs = activityLogs
		}
		return resp, nil
	})

	// Edit Node Type Email Address
	type EditNodeTypeEmailAddressInput struct {
		NodeID string `path:"nodeID" format:"uuid" doc:"Node ID" example:"962d5baa-6d73-4caa-b343-af32a3819805"`
		Body   struct {
			EmailAddress string `json:"email_address" format:"email" doc:"New email address"`
		}
	}

	type EditNodeTypeEmailAddressOutput struct {
		Body struct {
			NodeID       string `json:"node_id" format:"uuid" doc:"Node ID"`
			EmailAddress string `json:"email_address" format:"email" doc:"Updated email address"`
		}
	}

	huma.Register(api, huma.Operation{
		OperationID:   "edit-nodes-email-address",
		Method:        http.MethodPut,
		Path:          "/nodes/email_address/{nodeID}",
		Security:      []map[string][]string{{"microsoft_entra_auth": {"scope1"}}},
		Summary:       "Edit a Email Address Node",
		Tags:          []string{"Nodes, Email Address"},
		DefaultStatus: http.StatusOK,
	}, func(ctx context.Context, i *EditNodeTypeEmailAddressInput) (*EditNodeTypeEmailAddressOutput, error) {
		userID, ok := ctx.Value("userID").(string)
		if !ok {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}
		userIDPgType, err := converters.StringToPgTypeUUID(userID)
		if err != nil {
			return nil, huma.Error500InternalServerError("Something went wrong when fetching engagements")
		}
		resp := &EditNodeTypeEmailAddressOutput{}
		nodeIDPgType, err := converters.StringToPgTypeUUID(i.NodeID)
		if err != nil {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}

		updatedEmailAddressNode, err := a.queries.UpdateNodeTypeEmailAddress(context.Background(),
			db.UpdateNodeTypeEmailAddressParams{
				NodeID:       *nodeIDPgType,
				EmailAddress: i.Body.EmailAddress,
			})
		if err != nil {
			a.logger.Error("Error updating Email Node in database", "user_id", userID, "nodeID", i.NodeID, "error", err.Error())
			return nil, huma.Error500InternalServerError("Something went wrong")
		}

		// Update the nodes table with the name
		err = a.queries.UpdateNodeName(ctx, db.UpdateNodeNameParams{
			Name: i.Body.EmailAddress,
			ID:   *nodeIDPgType,
		})
		if err != nil {
			a.logger.Error("Error updating Node name in database", "user_id", userID, "nodeID", i.NodeID, "error", err.Error())
			return nil, huma.Error500InternalServerError("Something went wrong")
		}

		timestamp := pgtype.Timestamp{
			Time:             time.Now(),
			InfinityModifier: 0,
			Valid:            true,
		}

		err = activitylogs.InsertLog(a.queries, "Updated successfully", db.LogsNodesTypeEnumNODEUPDATE, *userIDPgType, *nodeIDPgType, timestamp)
		if err != nil {
			a.logger.Error("Error inserting log", "user_id", userID, "error", err.Error())
			return nil, huma.Error500InternalServerError("Something went wrong")
		}

		a.logger.Info("Updated Email Node successfully",
			"user_id", userID,
			"node_id", i.NodeID,
			"url", i.Body.EmailAddress,
		)

		resp.Body.EmailAddress = updatedEmailAddressNode.EmailAddress
		resp.Body.NodeID = i.NodeID

		return resp, nil
	})

	// Create Node Email Address
	type CreateNodeEmailAddressInput struct {
		Body struct {
			EngagementID string `json:"engagement_id" format:"uuid" doc:"Engagement ID" example:"b4ccc447-46bc-465d-8526-621f1cab1c8b"`
			NodeGroupID  string `json:"node_group_id,omitempty" format:"uuid" doc:"Node Group ID of the Engagement that the Node will be created in. If this attribute is not provided, a new Node Group will be created" example:"9a7e965f-43ed-434b-bf08-059e8dca0111"`
			EmailAddress string `json:"email_address" format:"email" example:"<EMAIL>"`
		}
	}

	huma.Register(api, huma.Operation{
		OperationID:   "post-nodes-email-address",
		Method:        http.MethodPost,
		Path:          "/nodes/email-address",
		Security:      []map[string][]string{{"microsoft_entra_auth": {"scope1"}}},
		Summary:       "Create an Email Address Node for an Engagement",
		Tags:          []string{"Nodes, Email Address"},
		DefaultStatus: http.StatusCreated,
	}, func(ctx context.Context, i *CreateNodeEmailAddressInput) (*struct{}, error) {
		userID, ok := ctx.Value("userID").(string)
		if !ok {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}
		userIDPgType, err := converters.StringToPgTypeUUID(userID)
		if err != nil {
			return nil, huma.Error500InternalServerError("Something went wrong when fetching engagements")
		}
		engagementIDPgType, err := converters.StringToPgTypeUUID(i.Body.EngagementID)
		if err != nil {
			a.logger.Error("Error parsing EngagementID", "user_id", userID, "error", err.Error())
			return nil, huma.Error500InternalServerError("Something went wrong")
		}

		var associatedNodeGroupID pgtype.UUID

		// No Node Group ID was provided to create the Node in, create a new one
		if len(i.Body.NodeGroupID) == 0 {
			associatedNodeGroup, err := a.queries.CreateNodeGroup(context.Background(), db.CreateNodeGroupParams{
				Name:         "New Node Group",
				IsActive:     true,
				EngagementID: *engagementIDPgType,
				CreatedAt: pgtype.Timestamp{
					Time:             time.Now(),
					InfinityModifier: 0,
					Valid:            true,
				},
				UpdatedAt: pgtype.Timestamp{
					Time:             time.Now(),
					InfinityModifier: 0,
					Valid:            true,
				},
			})
			if err != nil {
				a.logger.Error("Error creating new Node Group in database", "error", err.Error(),
					"user_id", userID,
					"engagement_id", i.Body.EngagementID,
					"node_type", "email_address",
					"node_group", i.Body.NodeGroupID)
				return nil, huma.Error500InternalServerError("Something went wrong")
			}
			associatedNodeGroupID = associatedNodeGroup.ID
		} else {
			// An existing Node Group ID was provided, check if it is associated with the Engagement
			// and associate it with the new Node
			nodeGroupIDPgType, err := converters.StringToPgTypeUUID(i.Body.NodeGroupID)
			if err != nil {
				a.logger.Error("Error parsing NodeGroupID", "user_id", userID, "error", err.Error())
				return nil, huma.Error500InternalServerError("Something went wrong")
			}
			nodeGroupsDB, err := a.queries.GetEngagementNodeGroup(context.Background(), db.GetEngagementNodeGroupParams{
				ID:           *nodeGroupIDPgType,
				EngagementID: *engagementIDPgType,
			})
			if err != nil {
				a.logger.Error("Error getting Node Groups", "user_id", userID, "error", err.Error(), "node_group_id", i.Body.NodeGroupID)
				return nil, huma.Error500InternalServerError("Something went wrong")
			}

			// If the Node Group is associated with the Engagement, use its ID
			// Otherwise return a generic error to the user for security reasons
			if len(nodeGroupsDB) == 1 {
				associatedNodeGroupID = nodeGroupsDB[0].ID
			} else {
				a.logger.Error("Error getting Node Group associated with Engagement while creating a Node",
					"user_id", userID,
					"node_group_id", i.Body.NodeGroupID,
					"engagement_id", i.Body.EngagementID,
					"node_type", "email_address")
				return nil, huma.Error500InternalServerError("Something went wrong")
			}
		}

		createdNode, err := a.queries.CreateNode(context.Background(), db.CreateNodeParams{
			NodeType:    "EMAIL_ADDRESS",
			Name:        i.Body.EmailAddress,
			NodeGroupID: associatedNodeGroupID,
		})
		if err != nil {
			a.logger.Error("Error creating Node in database", "error", err.Error(),
				"user_id", userID,
				"engagement_id", i.Body.EngagementID,
				"node_type", "email_address",
				"node_group", i.Body.NodeGroupID)
			return nil, huma.Error500InternalServerError("Something went wrong")
		}

		err = a.queries.CreateEmailAddressNode(context.Background(), db.CreateEmailAddressNodeParams{
			EmailAddress: i.Body.EmailAddress,
			NodeID:       createdNode.ID,
		})
		if err != nil {
			a.logger.Error("Error creating Node in database", "error", err.Error(),
				"user_id", userID,
				"engagement_id", i.Body.EngagementID,
				"node_type", "email_address",
				"node_group", i.Body.NodeGroupID)
			return nil, huma.Error500InternalServerError("Something went wrong")
		}

		timestamp := pgtype.Timestamp{
			Time:             time.Now(),
			InfinityModifier: 0,
			Valid:            true,
		}

		err = activitylogs.InsertLog(a.queries, "Created successfully", db.LogsNodesTypeEnumNODECREATION, *userIDPgType, createdNode.ID, timestamp)
		if err != nil {
			a.logger.Error("Error inserting log", "user_id", userID, "error", err.Error())
			return nil, huma.Error500InternalServerError("Something went wrong")
		}

		createdNodeIDString, err := converters.PgTypeUUIDToString(createdNode.ID)
		if err != nil {
			a.logger.Error("Error converting Node ID to string", "user_id", userID, "error", err.Error(), "node_type", createdNode.NodeType, "node_id", createdNode.ID)
			return nil, huma.Error500InternalServerError("Something went wrong")
		}
		a.logger.Info("Created Email Address Node successfully",
			"user_id", userID,
			"engagement_id", i.Body.EngagementID,
			"email_address", i.Body.EmailAddress,
			"node_id", createdNodeIDString,
		)
		return nil, nil
	})
}
