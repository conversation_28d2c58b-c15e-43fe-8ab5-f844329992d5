package main

import (
	"context"
	"net/http"
	"sort"

	"github.com/danielgtaylor/huma/v2"
	"github.com/jackc/pgx/v5/pgtype"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/converters"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/db"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/providers/azure"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/users"
)

func addUsersRoutes(api huma.API, a *application) {

	type GetUsersOutput struct {
		Body struct {
			Users []users.User `json:"users"`
		}
	}

	huma.Register(api, huma.Operation{
		OperationID:   "get-users",
		Method:        http.MethodGet,
		Path:          "/users",
		Security:      []map[string][]string{{"microsoft_entra_auth": {"scope1"}}},
		Summary:       "Get Users",
		Tags:          []string{"Users"},
		DefaultStatus: http.StatusOK,
	}, func(ctx context.Context, input *struct{}) (*GetUsersOutput, error) {
		_, ok := ctx.Value("userID").(string)
		if !ok {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}
		resp := &GetUsersOutput{}

		usersResults, err := users.GetUsers(a.queries)
		if err != nil {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}
		resp.Body.Users = usersResults
		return resp, nil
	})

	// Get user-management users
	type GetUserManagementOutput struct {
		Body struct {
			Users []azure.GroupMember `json:"users"`
		}
	}

	type GetUsersInput struct {
		UserType string `query:"type" enum:"standard,admin"`
	}

	huma.Register(api, huma.Operation{
		OperationID:   "get-user-management-users",
		Method:        http.MethodGet,
		Path:          "/users/user-management",
		Security:      []map[string][]string{{"microsoft_entra_auth": {"scope1"}}},
		Summary:       "Get User Management Users",
		Tags:          []string{"Users"},
		DefaultStatus: http.StatusOK,
	}, func(ctx context.Context, i *GetUsersInput) (*GetUserManagementOutput, error) {
		_, ok := ctx.Value("userID").(string)
		if !ok {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}
		resp := &GetUserManagementOutput{}

		accessToken, err := a.graphClient.GetAccessToken()
		if err != nil {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}

		groupMembers := make([]azure.GroupMember, 0)

		if i.UserType == "standard" {
			groupMembers, err = a.graphClient.GetGroupMembers(*accessToken, a.standardUsersGroupID, "standard")
			if err != nil {
				return nil, huma.Error500InternalServerError("Something went wrong")
			}
		} else if i.UserType == "admin" {
			groupMembers, err = a.graphClient.GetGroupMembers(*accessToken, a.adminUsersGroupID, "admin")
			if err != nil {
				return nil, huma.Error500InternalServerError("Something went wrong")
			}
		} else {
			standardGroupMembers, err := a.graphClient.GetGroupMembers(*accessToken, a.standardUsersGroupID, "standard")
			if err != nil {
				return nil, huma.Error500InternalServerError("Something went wrong")
			}
			adminGroupMembers, err := a.graphClient.GetGroupMembers(*accessToken, a.adminUsersGroupID, "admin")
			if err != nil {
				return nil, huma.Error500InternalServerError("Something went wrong")
			}
			groupMembers = append(groupMembers, standardGroupMembers...)
			groupMembers = append(groupMembers, adminGroupMembers...)
		}

		resp.Body.Users = groupMembers

		return resp, nil
	})

	// Get User details
	type GetUserDetailsInput struct {
		UserID string `path:"user_id" format:"uuid" doc:"User ID" example:"cdfb2590-b9b4-47ec-b2ea-3463a4da44f6"`
	}

	type GetUserDetailsOutput struct {
		Body struct {
			User users.User `json:"user"`
		}
	}

	huma.Register(api, huma.Operation{
		OperationID:   "get-user-details",
		Method:        http.MethodGet,
		Path:          "/users/{user_id}",
		Security:      []map[string][]string{{"microsoft_entra_auth": {"scope1"}}},
		Summary:       "Get User Details",
		Tags:          []string{"User"},
		DefaultStatus: http.StatusOK,
	}, func(ctx context.Context, input *GetUserDetailsInput) (*GetUserDetailsOutput, error) {
		userID, ok := ctx.Value("userID").(string)
		if !ok {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}

		userDetails, err := users.GetUserForUserSettings(a.queries, userID)
		if err != nil {
			return nil, huma.Error500InternalServerError("Failed to fetch user details", err)
		}

		resp := &GetUserDetailsOutput{}
		resp.Body.User = *userDetails

		return resp, nil
	})

	// Get User engagements details
	type GetUserEngagementTitlesInput struct {
		UserID string `path:"user_id" format:"uuid" doc:"User ID" example:"cdfb2590-b9b4-47ec-b2ea-3463a4da44f6"`
	}

	type GetUserEngagementTitlesOutput struct {
		Body struct {
			Engagements []string `json:"engagements"`
		}
	}

	huma.Register(api, huma.Operation{
		OperationID:   "get-user-engagement-titles",
		Method:        http.MethodGet,
		Path:          "/users/{user_id}/engagements",
		Security:      []map[string][]string{{"microsoft_entra_auth": {"scope1"}}},
		Summary:       "Get User Engagement Titles",
		Tags:          []string{"User"},
		DefaultStatus: http.StatusOK,
	}, func(ctx context.Context, input *GetUserEngagementTitlesInput) (*GetUserEngagementTitlesOutput, error) {
		userIDPgType, err := converters.StringToPgTypeUUID(input.UserID)
		if err != nil {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}

		resp := &GetUserEngagementTitlesOutput{}
		engagementsList, err := a.queries.GetUserEngagements(context.Background(), *userIDPgType)
		if err != nil {
			return nil, huma.Error500InternalServerError("Failed to fetch engagements", err)
		}
		var engagementTitles []string
		for _, engagement := range engagementsList {
			engagementTitles = append(engagementTitles, engagement.Title)
		}
		sort.Strings(engagementTitles)
		resp.Body.Engagements = engagementTitles

		return resp, nil
	})

	// Edit Custom Username script
	type EditUserUsernameInput struct {
		UserID string `path:"user_id" format:"uuid" doc:"User ID" example:"cdfb2590-b9b4-47ec-b2ea-3463a4da44f6"`
		Body   struct {
			CustomUsername string `json:"custom_username" doc:"Updated username of the user" example:"Updated User"`
		}
	}

	huma.Register(api, huma.Operation{
		OperationID:   "edit-user-username",
		Method:        http.MethodPut,
		Path:          "/users/{user_id}",
		Security:      []map[string][]string{{"microsoft_entra_auth": {"scope1"}}},
		Summary:       "Edit User username",
		Tags:          []string{"User", "Username"},
		DefaultStatus: http.StatusOK,
	}, func(ctx context.Context, input *EditUserUsernameInput) (*struct{}, error) {
		userID, ok := ctx.Value("userID").(string)
		if !ok {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}

		if input.UserID != userID {
			a.logger.Warn("User ID in path does not match authenticated user",
				"user_id", userID,
				"script_owner_user_id", input.UserID)
			return nil, huma.Error404NotFound("User not found")
		}

		err := users.UpdateUserUsername(a.queries, pgtype.Text{String: input.Body.CustomUsername, Valid: true}, userID)
		if err != nil {
			a.logger.Error("Error updating user in database", "error", err.Error())
			return nil, huma.Error500InternalServerError("Something went wrong", err)
		}

		if err := users.SyncAdminUserEngagements(*a.queries, context.Background(), userID); err != nil {
			return nil, huma.Error500InternalServerError("Something went wrong", err)
		}

		return nil, nil
	})

	type GetScriptsOutput struct {
		Body struct {
			Scripts []users.Script `json:"scripts"`
		}
	}

	// Get User scripts
	huma.Register(api, huma.Operation{
		OperationID:   "get-user-scripts",
		Method:        http.MethodGet,
		Path:          "/users/{user_id}/scripts",
		Security:      []map[string][]string{{"microsoft_entra_auth": {"scope1"}}},
		Summary:       "Get Scripts",
		Tags:          []string{"User, Scripts"},
		DefaultStatus: http.StatusOK,
	}, func(ctx context.Context, input *struct {
		UserID string `path:"user_id" format:"uuid" doc:"User ID" example:"cdfb2590-b9b4-47ec-b2ea-3463a4da44f6"`
	}) (*GetScriptsOutput, error) {
		userID, ok := ctx.Value("userID").(string)
		if !ok {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}

		if input.UserID != userID {
			a.logger.Warn("User ID in path does not match authenticated user",
				"user_id", userID,
				"script_owner_user_id", input.UserID)
			return nil, huma.Error404NotFound("User not found")
		}
		scriptsResult, err := users.GetScripts(a.queries, input.UserID)
		if err != nil {
			return nil, huma.Error500InternalServerError("Something went wrong", err)
		}

		resp := &GetScriptsOutput{}
		resp.Body.Scripts = scriptsResult
		return resp, nil
	})

	type DeleteScriptInput struct {
		UserID   string `path:"user_id" format:"uuid" doc:"User ID" example:"cdfb2590-b9b4-47ec-b2ea-3463a4da44f6"`
		ScriptID string `path:"script_id" format:"uuid" doc:"Script ID" example:"abcd1234"`
	}

	huma.Register(api, huma.Operation{
		OperationID:   "delete-user-script",
		Method:        http.MethodDelete,
		Path:          "/users/{user_id}/scripts/{script_id}",
		Security:      []map[string][]string{{"microsoft_entra_auth": {"scope1"}}},
		Summary:       "Delete Script",
		Tags:          []string{"User", "Scripts"},
		DefaultStatus: http.StatusNoContent,
	}, func(ctx context.Context, input *DeleteScriptInput) (*struct{}, error) {
		userID, ok := ctx.Value("userID").(string)
		if !ok {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}

		if input.UserID != userID {
			a.logger.Warn("User ID in path does not match authenticated user",
				"user_id", userID,
				"script_owner_user_id", input.UserID)
			return nil, huma.Error404NotFound("User not found")
		}

		if !ok {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}

		err := users.DeleteScript(a.queries, input.ScriptID, userID)

		if err != nil {
			return nil, huma.Error500InternalServerError("Something went wrong", err)
		}

		return nil, nil
	})

	// Create user script
	type CreateScriptInput struct {
		UserID string `path:"user_id" format:"uuid" doc:"User ID" example:"cdfb2590-b9b4-47ec-b2ea-3463a4da44f6"`
		Body   struct {
			Name        string            `json:"name" doc:"Name of the script" example:"My Script"`
			Description string            `json:"description" doc:"Description of the script" example:"This is a script description"`
			Content     string            `json:"content" doc:"Content of the script" example:"This is a script content"`
			ScriptType  db.ScriptTypeEnum `json:"script_type" enum:"STANDARD,ADMIN" doc:"Type of the script" example:"ADMIN"`
		}
	}

	type CreateScriptOutput struct {
		Body struct {
			Script users.Script `json:"script" doc:"Created User script"`
		}
	}

	huma.Register(api, huma.Operation{
		OperationID:   "post-user-script",
		Method:        http.MethodPost,
		Path:          "/users/{user_id}/scripts",
		Security:      []map[string][]string{{"microsoft_entra_auth": {"scope1"}}},
		Summary:       "Create Script",
		Tags:          []string{"User", "Scripts"},
		DefaultStatus: http.StatusCreated,
	}, func(ctx context.Context, input *CreateScriptInput) (*CreateScriptOutput, error) {
		userID, ok := ctx.Value("userID").(string)
		if !ok {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}

		if input.UserID != userID {
			a.logger.Warn("User ID in path does not match authenticated user",
				"user_id", userID,
				"script_owner_user_id", input.UserID)
			return nil, huma.Error404NotFound("User not found")
		}

		resp := &CreateScriptOutput{}

		newScript, err := users.CreateNewScript(a.queries, input.Body.Name, input.Body.Description, input.Body.Content, db.ScriptTypeEnumSTANDARD, userID)
		if err != nil {
			a.logger.Error("Error creating script in database", "error", err.Error())
			return nil, huma.Error500InternalServerError("Something went wrong", err)
		}

		resp.Body.Script = *newScript
		return resp, nil
	})

	// Edit User script
	type EditScriptInput struct {
		UserID   string `path:"user_id" format:"uuid" doc:"User ID" example:"cdfb2590-b9b4-47ec-b2ea-3463a4da44f6"`
		ScriptID string `path:"script_id" format:"uuid" doc:"Script ID" example:"abcd1234"`
		Body     struct {
			Name        string            `json:"name" doc:"Updated name of the script" example:"Updated Script"`
			Description string            `json:"description" doc:"Updated description of the script" example:"This is an updated script description"`
			Content     string            `json:"content" doc:"Content of the script" example:"This is a script content"`
			ScriptType  db.ScriptTypeEnum `json:"script_type" enum:"STANDARD,ADMIN" doc:"Type of the script" example:"ADMIN"`
		}
	}

	type EditScriptOutput struct {
		Body struct {
			Script users.Script `json:"script" doc:"User details"`
		}
	}

	huma.Register(api, huma.Operation{
		OperationID:   "edit-user-script",
		Method:        http.MethodPut,
		Path:          "/users/{user_id}/scripts/{script_id}",
		Security:      []map[string][]string{{"microsoft_entra_auth": {"scope1"}}},
		Summary:       "Edit Script",
		Tags:          []string{"User", "Scripts"},
		DefaultStatus: http.StatusOK,
	}, func(ctx context.Context, input *EditScriptInput) (*EditScriptOutput, error) {
		userID, ok := ctx.Value("userID").(string)
		if !ok {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}

		if input.UserID != userID {
			a.logger.Warn("User ID in path does not match authenticated user",
				"user_id", userID,
				"script_owner_user_id", input.UserID)
			return nil, huma.Error404NotFound("User not found")
		}

		updatedScript, err := users.UpdateScript(a.queries, input.ScriptID, input.Body.Name, input.Body.Description, input.Body.Content, db.ScriptTypeEnumSTANDARD, userID)
		if err != nil {
			a.logger.Error("Error updating script in database", "error", err.Error())
			return nil, huma.Error500InternalServerError("Something went wrong", err)
		}

		resp := &EditScriptOutput{}
		resp.Body.Script = *updatedScript
		return resp, nil
	})

	// Delete User SSH key
	type DeleteSshKeyInput struct {
		UserID string `path:"user_id" format:"uuid" doc:"User ID" example:"cdfb2590-b9b4-47ec-b2ea-3463a4da44f6"`
	}

	huma.Register(api, huma.Operation{
		OperationID:   "delete-user-ssh-key",
		Method:        http.MethodDelete,
		Path:          "/users/{user_id}/ssh-key",
		Security:      []map[string][]string{{"microsoft_entra_auth": {"scope1"}}},
		Summary:       "Delete User SSH Key",
		Tags:          []string{"User", "SSH Key"},
		DefaultStatus: http.StatusNoContent,
	}, func(ctx context.Context, input *DeleteSshKeyInput) (*struct{}, error) {
		userID, ok := ctx.Value("userID").(string)
		if !ok {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}

		if input.UserID != userID {
			a.logger.Warn("User ID in path does not match authenticated user",
				"user_id", userID,
				"script_owner_user_id", input.UserID)
			return nil, huma.Error404NotFound("User not found")
		}

		err := users.RemoveUserSshKey(a.queries, userID)
		if err != nil {
			return nil, huma.Error500InternalServerError("Could not remove user's SSH key", err)
		}

		return nil, nil
	})

	// Set User SSH key
	type SetSshKeyInput struct {
		UserID string `path:"user_id" format:"uuid" doc:"User ID" example:"cdfb2590-b9b4-47ec-b2ea-3463a4da44f6"`
		Body   struct {
			SshKey      string `json:"ssh_key" doc:"Public SSH Key"`
			SshKeyLabel string `json:"ssh_key_label" doc:"Label for the SSH Key"`
		}
	}

	type SetSshKeyOutput struct {
		Body struct {
			User users.User `json:"user" doc:"User details"`
		}
	}

	huma.Register(api, huma.Operation{
		OperationID:   "set-user-ssh-key",
		Method:        http.MethodPut,
		Path:          "/users/{user_id}/ssh-key",
		Security:      []map[string][]string{{"microsoft_entra_auth": {"scope1"}}},
		Summary:       "Set User SSH Key",
		Tags:          []string{"User", "SSH Key"},
		DefaultStatus: http.StatusOK,
	}, func(ctx context.Context, input *SetSshKeyInput) (*SetSshKeyOutput, error) {
		resp := &SetSshKeyOutput{}
		userID, ok := ctx.Value("userID").(string)
		if !ok {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}

		if input.UserID != userID {
			a.logger.Warn("User ID in path does not match authenticated user",
				"user_id", userID,
				"script_owner_user_id", input.UserID)
			return nil, huma.Error404NotFound("User not found")
		}

		updatedUser, err := users.SetUserSshKey(a.queries, userID, input.Body.SshKey, input.Body.SshKeyLabel)
		if err != nil {
			return nil, huma.Error500InternalServerError("Could not set user's SSH key", err)
		}

		if err := users.SyncAdminUserEngagements(*a.queries, context.Background(), userID); err != nil {
			return nil, huma.Error500InternalServerError("Something went wrong", err)
		}

		resp.Body.User = *updatedUser
		return resp, nil
	})
}
