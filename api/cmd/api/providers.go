package main

import (
	"context"
	"fmt"
	"net/http"

	"github.com/danielgtaylor/huma/v2"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/instances"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/providers/aws"
)

// addProvidersRoutes registers all /providers routes to the application router
func addProvidersRoutes(api huma.API, a *application) {
	type GetAmisOutput struct {
		Body struct {
			Amis []aws.AMI `json:"amis"`
		}
	}

	// Get AWS AMIs for a given region
	huma.Register(api, huma.Operation{
		OperationID:   "get-providers-aws-amis",
		Method:        http.MethodGet,
		Path:          "/providers/aws/amis/{region}",
		Security:      []map[string][]string{{"microsoft_entra_auth": {"scope1"}}},
		Summary:       "Get AWS AMIs for a region",
		Tags:          []string{"Providers, AWS, AMIs"},
		DefaultStatus: http.StatusOK,
	}, func(ctx context.Context, input *struct {
		Region string `path:"region" maxLength:"20" example:"eu-west-2" doc:"AWS Region"`
	}) (*GetAmisOutput, error) {
		resp := &GetAmisOutput{}
		awsEc2Client := aws.NewEc2Client(input.Region)
		resp.Body.Amis = awsEc2Client.GetAllAmis()
		return resp, nil
	})

	type GetRegionsOutput struct {
		Body struct {
			Regions            []string `json:"regions"`
			PrioritizedRegions []string `json:"prioritizedRegions"`
		}
	}

	// Get available AWS regions
	huma.Register(api, huma.Operation{
		OperationID:   "get-providers-aws-regions",
		Method:        http.MethodGet,
		Path:          "/providers/aws/regions",
		Security:      []map[string][]string{{"microsoft_entra_auth": {"scope1"}}},
		Summary:       "Get available AWS Regions",
		Tags:          []string{"Providers, AWS, Regions"},
		DefaultStatus: http.StatusOK,
	}, func(ctx context.Context, input *struct {
	}) (*GetRegionsOutput, error) {
		resp := &GetRegionsOutput{}
		awsEc2Client := aws.NewEc2Client(a.awsRootRegion)
		resp.Body.Regions, _ = awsEc2Client.GetRegions()
		resp.Body.PrioritizedRegions, _ = awsEc2Client.GetPrioritizedRegions(a.queries)
		return resp, nil
	})

	type GetAzureRegionsInput struct {
		TenantID string `query:"tenantId,required" doc:"Azure Tenant ID"`
		Provider string `query:"provider,required" doc:"Provider (e.g., azure)"`
	}

	// Get available Azure regions
	huma.Register(api, huma.Operation{
		OperationID:   "get-providers-azure-regions",
		Method:        http.MethodGet,
		Path:          "/providers/azure/regions",
		Security:      []map[string][]string{{"microsoft_entra_auth": {"scope1"}}},
		Summary:       "Get available Azure VM Regions",
		Tags:          []string{"Providers", "Azure", "Regions"},
		DefaultStatus: http.StatusOK,
	}, func(ctx context.Context, input *GetAzureRegionsInput) (*GetRegionsOutput, error) {

		regions, err := instances.FetchAzureVMRegions(ctx, a.queries, input.TenantID)
		if err != nil {
			return nil, fmt.Errorf("failed to fetch Azure regions: %w", err)
		}

		output := &GetRegionsOutput{}
		output.Body.Regions = append(output.Body.Regions, regions...)
		return output, nil

	})

	type GetAzureAmisOutput struct {
		Body struct {
			Amis []instances.AzureAMI `json:"amis"`
		}
	}
	// Register Azure AMIs endpoint
	huma.Register(api, huma.Operation{
		OperationID:   "get-providers-azure-amis",
		Method:        http.MethodGet,
		Path:          "/providers/azure/amis/{tenantID}/{region}/{instanceType}",
		Security:      []map[string][]string{{"microsoft_entra_auth": {"scope1"}}},
		Summary:       "Get Azure AMIs for a region",
		Tags:          []string{"Providers", "Azure", "AMIs"},
		DefaultStatus: http.StatusOK,
	}, func(ctx context.Context, input *struct {
		TenantID     string `path:"tenantID" maxLength:"64" doc:"Azure Tenant ID"`
		Region       string `path:"region" maxLength:"20" example:"westeurope" doc:"Azure Region"`
		InstanceType string `path:"instanceType" maxLength:"20" example:"westeurope" doc:"Azure Region"`
	}) (*GetAzureAmisOutput, error) {
		resp := &GetAzureAmisOutput{}
		// TODO: Remake this to show in cloud instances creation dropdown for AMIS only amis that are valid for the VM size
		// So, user first should select tenant id, then region, then VM size, then AMI
		resp.Body.Amis = instances.GetHardcodedAzureAmis(ctx)
		return resp, nil
	})

	type GetInstanceTypesDBOutput struct {
		Body struct {
			InstanceTypes []struct {
				Alias string `json:"alias"`
				Type  string `json:"type"`
			} `json:"instance_types"`
		}
	}

	huma.Register(api, huma.Operation{
		OperationID:   "get-azure-instance-types",
		Method:        http.MethodGet,
		Path:          "/nodes/cloud_instance/azure-instance-types/{region}",
		Security:      []map[string][]string{{"microsoft_entra_auth": {"scope1"}}},
		Summary:       "Get Azure instance types (hardcoded for now)",
		Tags:          []string{"Nodes, Cloud Instance, Instances"},
		DefaultStatus: http.StatusOK,
	}, func(ctx context.Context, i *struct {
		Region string `path:"region" maxLength:"20" example:"eastus" doc:"Azure Region"`
	}) (*GetInstanceTypesDBOutput, error) {

		// Hardcoded Azure instance types - TODO: all instance types can be retrieved per tenantt and region
		instanceTypes := []struct {
			Alias string
			Type  string
		}{
			{Alias: "Standard_B1ms", Type: "Standard_B1ms"},
			{Alias: "Standard_B1s", Type: "Standard_B1s"},
			{Alias: "Standard_B2s", Type: "Standard_B2s"},
		}

		resp := &GetInstanceTypesDBOutput{}
		resp.Body.InstanceTypes = make([]struct {
			Alias string `json:"alias"`
			Type  string `json:"type"`
		}, len(instanceTypes))

		for idx, it := range instanceTypes {
			resp.Body.InstanceTypes[idx] = struct {
				Alias string `json:"alias"`
				Type  string `json:"type"`
			}{
				Alias: it.Alias,
				Type:  it.Type,
			}
		}

		return resp, nil
	})

}
