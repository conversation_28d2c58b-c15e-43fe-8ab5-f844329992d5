package main

import (
	"context"
	"net/http"

	"github.com/danielgtaylor/huma/v2"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/clients"
)

// addClientsRoutes registers all /clients routes to the application router
func addClientsRoutes(api huma.API, a *application) {
	type GetClientsOutput struct {
		Body struct {
			Clients []string `json:"clients"`
		}
	}

	// Get all Engage clients
	huma.Register(api, huma.Operation{
		OperationID:   "get-clients",
		Method:        http.MethodGet,
		Path:          "/clients",
		Security:      []map[string][]string{{"microsoft_entra_auth": {"scope1"}}},
		Summary:       "Get Clients",
		Tags:          []string{"Clients"},
		DefaultStatus: http.StatusOK,
	}, func(ctx context.Context, input *struct{}) (*GetClientsOutput, error) {
		resp := &GetClientsOutput{}
		clientsResults, err := clients.GetClients(a.queries)
		if err != nil {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}
		resp.Body.Clients = clientsResults
		return resp, nil
	})

	type CreateClientInput struct {
		Body struct {
			Name string `json:"name" example:"TestClient"`
		}
	}

	// Create a Client
	huma.Register(api, huma.Operation{
		OperationID:   "create-client",
		Method:        http.MethodPost,
		Path:          "/client",
		Security:      []map[string][]string{{"microsoft_entra_auth": {"scope1"}}},
		Summary:       "Create Client",
		Tags:          []string{"Clients"},
		DefaultStatus: http.StatusCreated,
	}, func(ctx context.Context, i *CreateClientInput) (*struct{}, error) {
		// Insert the new Client in the database
		err := a.queries.CreateClient(context.Background(), i.Body.Name)
		if err != nil {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}
		return nil, nil
	})
}
