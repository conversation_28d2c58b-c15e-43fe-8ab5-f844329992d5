package main

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"github.com/danielgtaylor/huma/v2"
	"github.com/jackc/pgx/v5/pgtype"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/activitylogs"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/converters"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/db"
)

func addNodeTypePersonRoutes(api huma.API, a *application) {
	// Get Node Type Person
	type GetNodeTypePersonInput struct {
		NodeID              string `path:"nodeID" format:"uuid" doc:"Node ID" example:"48955848-1ee7-4d3d-8bdf-b48e75627ffc"`
		IncludeActivityLogs bool   `query:"activity_logs" format:"bool" doc:"Whether to include activity logs (optional). Example: true or false." default:"true"`
	}

	type NodePerson struct {
		FirstName string `json:"first_name"`
		LastName  string `json:"last_name"`
		Email     string `json:"email"`
		Company   string `json:"company"`
		Title     string `json:"title"`
		NodeID    string `json:"node_id" format:"uuid" doc:"Node ID"`
	}

	type GetNodeTypePersonOutput struct {
		Body struct {
			Node         NodePerson    `json:"node"`
			ActivityLogs []ActivityLog `json:"activity_logs,omitempty" doc:"Activity Logs"`
		}
	}
	huma.Register(api, huma.Operation{
		OperationID:   "get-nodes-person",
		Method:        http.MethodGet,
		Path:          "/nodes/person/{nodeID}",
		Security:      []map[string][]string{{"microsoft_entra_auth": {"scope1"}}},
		Summary:       "Get a Person Node",
		Tags:          []string{"Nodes, Person"},
		DefaultStatus: http.StatusOK,
	}, func(ctx context.Context, i *GetNodeTypePersonInput) (*GetNodeTypePersonOutput, error) {
		userID, ok := ctx.Value("userID").(string)
		if !ok {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}
		resp := &GetNodeTypePersonOutput{}
		nodeIDPgType, err := converters.StringToPgTypeUUID(i.NodeID)
		if err != nil {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}

		userIDPgType, err := converters.StringToPgTypeUUID(userID)
		if err != nil {
			return nil, err
		}
		node, err := a.queries.GetNodeTypePerson(context.Background(), db.GetNodeTypePersonParams{
			NodeID: *nodeIDPgType,
			ID:     *userIDPgType,
		})
		if err != nil {
			a.logger.Error("Error getting Node Person", "user_id", userID, "nodeID", i.NodeID, "error", err.Error())
			return nil, huma.Error404NotFound("Node not found")
		}

		resp.Body.Node.FirstName = node.FirstName
		resp.Body.Node.LastName = node.LastName.String
		resp.Body.Node.Email = node.Email.String
		resp.Body.Node.Company = node.Company.String
		resp.Body.Node.Title = node.Title.String
		resp.Body.Node.NodeID = i.NodeID

		if i.IncludeActivityLogs {
			activityLogs := make([]ActivityLog, 0)
			activityLogsDB, err := a.queries.GetNodeActivityLogs(context.Background(), *nodeIDPgType)
			if err != nil {
				a.logger.Error("Error getting activity logs", "user_id", userID, "nodeID", i.NodeID, "error", err.Error())
				return nil, huma.Error404NotFound("Activity logs not found")
			}
			for _, activityLogDB := range activityLogsDB {
				activityLogs = append(activityLogs, ActivityLog{
					Message:   activityLogDB.Message,
					Type:      activityLogDB.Type,
					Username:  activityLogDB.Username,
					CreatedAt: activityLogDB.CreatedAt.Time,
				})
			}
			resp.Body.ActivityLogs = activityLogs
		}
		return resp, nil
	})

	// Edit Node Type Person
	type EditNodeTypePersonInput struct {
		NodeID string `path:"nodeID" format:"uuid" doc:"Node ID" example:"48955848-1ee7-4d3d-8bdf-b48e75627ffc"`
		Body   struct {
			FirstName string `json:"first_name"`
			LastName  string `json:"last_name,omitempty"`
			Email     string `json:"email,omitempty"`
			Company   string `json:"company,omitempty"`
			Title     string `json:"title,omitempty"`
		}
	}

	type EditNodeTypePersonOutput struct {
		FirstName string `json:"first_name"`
		LastName  string `json:"last_name"`
		Email     string `json:"email"`
		Company   string `json:"company"`
		Title     string `json:"title"`
		NodeID    string `json:"node_id" format:"uuid" doc:"Node ID"`
	}

	huma.Register(api, huma.Operation{
		OperationID:   "edit-nodes-person",
		Method:        http.MethodPut,
		Path:          "/nodes/person/{nodeID}",
		Security:      []map[string][]string{{"microsoft_entra_auth": {"scope1"}}},
		Summary:       "Update a Person Node for an Engagement",
		Tags:          []string{"Nodes, Person"},
		DefaultStatus: http.StatusOK,
	}, func(ctx context.Context, i *EditNodeTypePersonInput) (*EditNodeTypePersonOutput, error) {
		userID, ok := ctx.Value("userID").(string)
		if !ok {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}
		userIDPgType, err := converters.StringToPgTypeUUID(userID)
		if err != nil {
			return nil, huma.Error500InternalServerError("Something went wrong when fetching engagements")
		}
		nodeIDPgType, err := converters.StringToPgTypeUUID(i.NodeID)
		if err != nil {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}

		resp := &EditNodeTypePersonOutput{}

		lastNamePgType := pgtype.Text{String: i.Body.LastName, Valid: true}
		emailPgType := pgtype.Text{String: i.Body.Email, Valid: true}
		companyPgType := pgtype.Text{String: i.Body.Company, Valid: true}
		titlePgType := pgtype.Text{String: i.Body.Title, Valid: true}

		updatedNodeTypePerson, err := a.queries.UpdateNodeTypePerson(context.Background(),
			db.UpdateNodeTypePersonParams{
				NodeID:    *nodeIDPgType,
				FirstName: i.Body.FirstName,
				LastName:  lastNamePgType,
				Email:     emailPgType,
				Company:   companyPgType,
				Title:     titlePgType,
			})
		if err != nil {
			a.logger.Error("Error updating Person Node in database", "user_id", userID, "nodeID", i.NodeID, "error", err.Error())
			return nil, huma.Error500InternalServerError("Something went wrong")
		}

		// Update the nodes table with the name
		updateNodeNameParams := db.UpdateNodeNameParams{
			ID:   *nodeIDPgType,
			Name: i.Body.FirstName + " " + i.Body.LastName,
		}

		err = a.queries.UpdateNodeName(ctx, updateNodeNameParams)
		if err != nil {
			a.logger.Error("Error updating Node name in database", "user_id", userID, "nodeID", i.NodeID, "error", err.Error())
			return nil, huma.Error500InternalServerError("Something went wrong")
		}

		timestamp := pgtype.Timestamp{
			Time:             time.Now(),
			InfinityModifier: 0,
			Valid:            true,
		}

		err = activitylogs.InsertLog(a.queries, "Updated successfully", db.LogsNodesTypeEnumNODEUPDATE, *userIDPgType, *nodeIDPgType, timestamp)
		if err != nil {
			a.logger.Error("Error inserting log", "user_id", userID, "error", err.Error())
			return nil, huma.Error500InternalServerError("Something went wrong")
		}

		resp.NodeID = i.NodeID
		resp.FirstName = updatedNodeTypePerson.FirstName
		resp.LastName = updatedNodeTypePerson.LastName.String
		resp.Email = updatedNodeTypePerson.Email.String
		resp.Company = updatedNodeTypePerson.Company.String
		resp.Title = updatedNodeTypePerson.Title.String

		a.logger.Info("Updated Person Node successfully",
			"user_id", userID,
			"node_id", i.NodeID,
			"first_name", i.Body.FirstName,
			"last_name", i.Body.LastName,
			"email", i.Body.Email,
			"company", i.Body.Company,
			"title", i.Body.Title,
		)

		return resp, nil
	})

	// Create Node Type Person
	type CreateNodeTypePersonInput struct {
		Body struct {
			EngagementID string `json:"engagement_id" format:"uuid" doc:"Engagement ID"  example:"b4ccc447-46bc-465d-8526-621f1cab1c8b"`
			NodeGroupID  string `json:"node_group_id,omitempty" format:"uuid" doc:"Node Group ID of the Engagement that the Node will be created in. If this attribute is not provided, a new Node Group will be created" example:"9a7e965f-43ed-434b-bf08-059e8dca0111"`
			FirstName    string `json:"first_name" maxLength:"100" example:"John"`
			LastName     string `json:"last_name,omitempty" maxLength:"100" example:"Doe"`
			Email        string `json:"email,omitempty" format:"email" example:"<EMAIL>"`
			Company      string `json:"company,omitempty" maxLength:"100" example:"ACME"`
			Title        string `json:"title,omitempty" maxLength:"100" example:"CEO"`
		}
	}

	huma.Register(api, huma.Operation{
		OperationID:   "post-nodes-person",
		Method:        http.MethodPost,
		Path:          "/nodes/person",
		Security:      []map[string][]string{{"microsoft_entra_auth": {"scope1"}}},
		Summary:       "Create an Person Node for an Engagement",
		Tags:          []string{"Nodes, Person"},
		DefaultStatus: http.StatusCreated,
	}, func(ctx context.Context, i *CreateNodeTypePersonInput) (*struct{}, error) {
		userID, ok := ctx.Value("userID").(string)
		if !ok {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}
		userIDPgType, err := converters.StringToPgTypeUUID(userID)
		if err != nil {
			return nil, huma.Error500InternalServerError("Something went wrong when fetching engagements")
		}
		engagementIDPgType, err := converters.StringToPgTypeUUID(i.Body.EngagementID)
		if err != nil {
			a.logger.Error("Error parsing EngagementID", "user_id", userID, "error", err.Error())
			return nil, huma.Error500InternalServerError("Something went wrong")
		}

		var associatedNodeGroupID pgtype.UUID

		// No Node Group ID was provided to create the Node in, create a new one
		if len(i.Body.NodeGroupID) == 0 {
			associatedNodeGroup, err := a.queries.CreateNodeGroup(context.Background(), db.CreateNodeGroupParams{
				Name:         "New Node Group",
				IsActive:     true,
				EngagementID: *engagementIDPgType,
				CreatedAt: pgtype.Timestamp{
					Time:             time.Now(),
					InfinityModifier: 0,
					Valid:            true,
				},
				UpdatedAt: pgtype.Timestamp{
					Time:             time.Now(),
					InfinityModifier: 0,
					Valid:            true,
				},
			})
			if err != nil {
				a.logger.Error("Error creating new Node Group in database", "error", err.Error(),
					"user_id", userID,
					"engagement_id", i.Body.EngagementID,
					"node_type", "person",
					"node_group", i.Body.NodeGroupID)
				return nil, huma.Error500InternalServerError("Something went wrong")
			}
			associatedNodeGroupID = associatedNodeGroup.ID
		} else {
			// An existing Node Group ID was provided, check if it is associated with the Engagement
			// and associate it with the new Node
			nodeGroupIDPgType, err := converters.StringToPgTypeUUID(i.Body.NodeGroupID)
			if err != nil {
				a.logger.Error("Error parsing NodeGroupID", "user_id", userID, "error", err.Error())
				return nil, huma.Error500InternalServerError("Something went wrong")
			}
			nodeGroupsDB, err := a.queries.GetEngagementNodeGroup(context.Background(), db.GetEngagementNodeGroupParams{
				ID:           *nodeGroupIDPgType,
				EngagementID: *engagementIDPgType,
			})
			if err != nil {
				a.logger.Error("Error getting Node Groups", "user_id", userID, "error", err.Error(), "node_group_id", i.Body.NodeGroupID)
				return nil, huma.Error500InternalServerError("Something went wrong")
			}

			// If the Node Group is associated with the Engagement, use its ID
			// Otherwise return a generic error to the user for security reasons
			if len(nodeGroupsDB) == 1 {
				associatedNodeGroupID = nodeGroupsDB[0].ID
			} else {
				a.logger.Error("Error getting Node Group associated with Engagement while creating a Node",
					"user_id", userID,
					"node_group_id", i.Body.NodeGroupID,
					"engagement_id", i.Body.EngagementID,
					"node_type", "person")
				return nil, huma.Error500InternalServerError("Something went wrong")
			}
		}

		createdNode, err := a.queries.CreateNode(context.Background(), db.CreateNodeParams{
			NodeType:    "PERSON",
			Name:        fmt.Sprintf("%s %s", i.Body.FirstName, i.Body.LastName),
			NodeGroupID: associatedNodeGroupID,
		})
		if err != nil {
			a.logger.Error("Error creating Node in database", "error", err.Error(),
				"user_id", userID,
				"engagement_id", i.Body.EngagementID,
				"node_type", "person",
				"node_group", i.Body.NodeGroupID)
			return nil, huma.Error500InternalServerError("Something went wrong")
		}

		createPersonNodeParams := db.CreatePersonNodeParams{
			FirstName: i.Body.FirstName,
			LastName:  pgtype.Text{String: i.Body.LastName, Valid: true},
			Email:     pgtype.Text{String: i.Body.Email, Valid: true},
			Company:   pgtype.Text{String: i.Body.Company, Valid: true},
			Title:     pgtype.Text{String: i.Body.Title, Valid: true},
			NodeID:    createdNode.ID,
		}

		if len(i.Body.LastName) == 0 {
			createPersonNodeParams.LastName.Valid = false
		}
		if len(i.Body.Email) == 0 {
			createPersonNodeParams.Email.Valid = false
		}
		if len(i.Body.Company) == 0 {
			createPersonNodeParams.Company.Valid = false
		}
		if len(i.Body.Title) == 0 {
			createPersonNodeParams.Title.Valid = false
		}

		err = a.queries.CreatePersonNode(context.Background(), createPersonNodeParams)
		if err != nil {
			a.logger.Error("Error creating Node in database", "error", err.Error(),
				"user_id", userID,
				"engagement_id", i.Body.EngagementID,
				"node_type", "url",
				"node_group", i.Body.NodeGroupID)
			return nil, huma.Error500InternalServerError("Something went wrong")
		}

		createdNodeIDString, err := converters.PgTypeUUIDToString(createdNode.ID)
		if err != nil {
			a.logger.Error("Error converting Node ID to string", "error", err.Error(), "node_type", createdNode.NodeType, "node_id", createdNode.ID)
			return nil, huma.Error500InternalServerError("Something went wrong")
		}
		a.logger.Info("Created Person Node successfully",
			"user_id", userID,
			"engagement_id", i.Body.EngagementID,
			"first_name", i.Body.FirstName,
			"last_name", i.Body.LastName,
			"email", i.Body.Email,
			"company", i.Body.Company,
			"title", i.Body.Title,
			"node_id", createdNodeIDString,
		)

		timestamp := pgtype.Timestamp{
			Time:             time.Now(),
			InfinityModifier: 0,
			Valid:            true,
		}

		err = activitylogs.InsertLog(a.queries, "Created successfully", db.LogsNodesTypeEnumNODECREATION, *userIDPgType, createdNode.ID, timestamp)
		if err != nil {
			a.logger.Error("Error inserting log", "user_id", userID, "error", err.Error())
			return nil, huma.Error500InternalServerError("Something went wrong")
		}

		return nil, nil
	})
}
