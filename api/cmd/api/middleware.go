package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"strings"
	"time"

	"github.com/danielgtaylor/huma/v2"
	"github.com/google/uuid"
	"github.com/jackc/pgx/v5/pgtype"
	"github.com/lestrrat-go/jwx/v2/jwa"
	"github.com/lestrrat-go/jwx/v2/jwk"
	"github.com/lestrrat-go/jwx/v2/jws"
	"github.com/lestrrat-go/jwx/v2/jwt"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/db"
)

// NewJWKSet creates an auto-refreshing key set to validate JWT signatures.
func NewJWKSet(jwkUrl string) jwk.Set {
	jwkCache := jwk.NewCache(context.Background())

	// register a minimum refresh interval for this URL.
	// when not specified, defaults to Cache-Control and similar resp headers
	err := jwkCache.Register(jwkUrl, jwk.WithMinRefreshInterval(10*time.Minute))
	if err != nil {
		panic("failed to register jwk location")
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// fetch once on application startup
	_, err = jwkCache.Refresh(ctx, jwkUrl)
	if err != nil {
		panic("failed to fetch on startup")
	}
	// create the cached key set
	return jwk.NewCachedSet(jwkCache, jwkUrl)
}

// NewAuthMiddleware creates a middleware that will authorize requests based on
// the required scopes for the operation.

func NewAuthMiddleware(api huma.API, appInstance *application, jwksURL string, azureAppID string, azureTenantID string, standardUsersGroupID string, adminUsersGroupID string) func(ctx huma.Context, next func(huma.Context)) {

	keySet := NewJWKSet(jwksURL)

	return func(ctx huma.Context, next func(huma.Context)) {
		// Retrieve JWT from Authorization header
		token := strings.TrimPrefix(ctx.Header("Authorization"), "Bearer ")
		if len(token) == 0 {
			log.Println("no Authorization header found")
			_ = huma.WriteErr(api, ctx, http.StatusUnauthorized, "Unauthorized")
			return
		}

		// Parse the JWT and extract the "kid" from the header
		message, err := jws.ParseString(token)

		if err != nil {
			_ = huma.WriteErr(api, ctx, http.StatusUnauthorized, "Unauthorized")
			return
		}
		if message.Signatures() == nil {
			_ = huma.WriteErr(api, ctx, http.StatusUnauthorized, "Unauthorized")
			return
		}
		if len(message.Signatures()) == 0 {
			_ = huma.WriteErr(api, ctx, http.StatusUnauthorized, "Unauthorized")
			return
		}
		headers := message.Signatures()[0].ProtectedHeaders()
		kid := headers.KeyID()

		//Lookup key against previously fetched key set
		signingKey, keyFound := keySet.LookupKeyID(kid)
		if !keyFound {
			_ = huma.WriteErr(api, ctx, http.StatusUnauthorized, "Unauthorized")
			return
		}

		// Parse and verify the JWT
		parsed, err := jwt.ParseString(token,
			jwt.WithKey(jwa.RS256, signingKey),
			jwt.WithValidate(true),
			jwt.WithIssuer(fmt.Sprintf("https://login.microsoftonline.com/%s/v2.0", azureTenantID)),
			jwt.WithAudience(azureAppID),
		)
		if err != nil {
			log.Printf("error parsing JWT: %s\n", err.Error())
			_ = huma.WriteErr(api, ctx, http.StatusUnauthorized, "Unauthorized")
			return
		}

		// Set parsed values in the endpoint context
		userID, found := parsed.Get("oid")
		if !found {
			log.Printf("Claim 'oid' not found in JWT")
			_ = huma.WriteErr(api, ctx, http.StatusUnauthorized, "Unauthorized")
			return
		}
		ctx = huma.WithValue(ctx, "userID", userID)

		roles, found := parsed.Get("roles")
		if !found {
			log.Printf("Claim 'roles' not found in JWT")
			_ = huma.WriteErr(api, ctx, http.StatusUnauthorized, "Unauthorized")
			return
		}
		roleList, ok := roles.([]interface{})
		if !ok {
			log.Printf("Failed to parse roles as a list")
			_ = huma.WriteErr(api, ctx, http.StatusUnauthorized, "Unauthorized")
			return
		}

		parsedRoles := make([]string, 0)
		for _, role := range roleList {
			roleStr, ok := role.(string)
			if !ok {
				log.Printf("Failed to parse a role as string")
				_ = huma.WriteErr(api, ctx, http.StatusUnauthorized, "Unauthorized")
				return
			}

			switch roleStr {
			case "Engage.Standard":
				parsedRoles = append(parsedRoles, "Standard")
			case "Engage.Admin":
				parsedRoles = append(parsedRoles, "Admin")
			}
		}

		if len(parsedRoles) == 0 {
			log.Printf("No relevant roles found for user")
			_ = huma.WriteErr(api, ctx, http.StatusUnauthorized, "Unauthorized")
			return
		}

		ctx = huma.WithValue(ctx, "roles", parsedRoles)

		preferredUsername, found := parsed.Get("preferred_username")
		if !found {
			log.Printf("Claim 'preferred_username' not found in JWT")
			_ = huma.WriteErr(api, ctx, http.StatusUnauthorized, "Unauthorized")
			return
		}
		ctx = huma.WithValue(ctx, "preferred_username", preferredUsername)

		name, found := parsed.Get("name")
		if !found {
			log.Printf("Claim 'name' not found in JWT")
			_ = huma.WriteErr(api, ctx, http.StatusUnauthorized, "Unauthorized")
			return
		}
		ctx = huma.WithValue(ctx, "name", name)

		// Convert interface{} to string
		userIDStr, ok := userID.(string)
		if !ok {
			log.Println("OID claim is not a valid string")
			_ = huma.WriteErr(api, ctx, http.StatusUnauthorized, "Invalid User ID")
			return
		}

		parsedUUID, err := uuid.Parse(userIDStr)
		if err != nil {
			log.Printf("Invalid UUID format: %s", userIDStr)
			_ = huma.WriteErr(api, ctx, http.StatusUnauthorized, "Invalid User ID")
			return
		}

		userIDPgType := pgtype.UUID{Bytes: parsedUUID, Valid: true}

		if err := reconcileAzureUser(context.Background(), appInstance, userIDPgType, parsedRoles, preferredUsername.(string), name.(string)); err != nil {
			log.Printf("User sync failed: %s", err)
			_ = huma.WriteErr(api, ctx, http.StatusUnauthorized, "Failed to sync user")
			return
		}

		next(ctx)
	}
}

func reconcileAzureUser(ctx context.Context, app *application, userID pgtype.UUID, roles []string, preferredUsername string, name string) error {
	_, err := app.queries.GetUserByAzureID(ctx, userID)
	if err != nil {
		if err = app.queries.CreateUser(ctx, db.CreateUserParams{
			ID:       userID,
			FullName: pgtype.Text{String: name, Valid: true},
			Username: preferredUsername,
			AppRole:  pgtype.Text{String: roles[0], Valid: true},
		}); err != nil {
			return fmt.Errorf("failed to create user: %w", err)
		}
	}
	return nil
}
