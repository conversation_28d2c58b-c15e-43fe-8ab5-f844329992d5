package db

import (
	"context"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgconn"
)

// ExecRaw exposes a safe way to execute custom SQL against the underlying DB connection.
func (q *Queries) ExecRaw(ctx context.Context, sql string, args ...interface{}) (pgconn.CommandTag, error) {
	return q.db.Exec(ctx, sql, args...)
}

// QueryRaw exposes a safe way to run custom queries when needed.
func (q *Queries) QueryRaw(ctx context.Context, sql string, args ...interface{}) (pgx.Rows, error) {
	return q.db.Query(ctx, sql, args...)
}

