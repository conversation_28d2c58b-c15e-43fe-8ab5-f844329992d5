package authz

import (
	"context"
	"errors"
)

// HasRole checks whether the current request context includes the given role.
func <PERSON><PERSON>ole(ctx context.Context, role string) bool {
	roles, ok := ctx.Value("roles").([]string)
	if !ok || len(roles) == 0 {
		return false
	}
	for _, r := range roles {
		if r == role {
			return true
		}
	}
	return false
}

// RequireAdmin returns an error if the current user is not an Admin.
// Admin role values are populated by NewAuthMiddleware in middleware.go which maps
// Azure AD roles to short names (e.g., "Engage.Admin" -> "Admin").
func RequireAdmin(ctx context.Context) error {
	if !HasRole(ctx, "Admin") {
		return errors.New("unauthorized: admin role required")
	}
	return nil
}

