package cloudinstance

import (
	"context"
	"encoding/json"
	"fmt"
	"log/slog"
	"net/netip"
	"strings"
	"time"

	"github.com/Azure/azure-sdk-for-go/sdk/resourcemanager/compute/armcompute"
	"github.com/Azure/azure-sdk-for-go/sdk/resourcemanager/network/armnetwork"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/credentials"
	"github.com/aws/aws-sdk-go-v2/service/ec2"
	"github.com/jackc/pgx/v5/pgtype"
	amqp "github.com/rabbitmq/amqp091-go"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/converters"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/db"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/deployments/keys"
	azure "gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/providers/azure"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/utils"
)

// CloudInstanceStateCheckTask represents a task to check and log cloud instance state changes
type CloudInstanceStateCheckTask struct {
	NodeID        string `json:"node_id"`
	InstanceID    string `json:"instance_id"`
	Action        string `json:"action"`
	UserID        string `json:"user_id"`
	OldIP         string `json:"old_ip,omitempty"`
	ExpectedState string `json:"expected_state"`
	Provider      string `json:"provider"` // AWS, AZURE, etc.
	Region        string `json:"region"`
	AccountID     string `json:"account_id"`
	Timestamp     int64  `json:"timestamp"`
}

// PublishStateCheckTask publishes a cloud instance state check task to RabbitMQ
func PublishStateCheckTask(ctx context.Context, ch *amqp.Channel, queueName string, task CloudInstanceStateCheckTask) error {
	task.Timestamp = time.Now().Unix()

	body, err := json.Marshal(task)
	if err != nil {
		return fmt.Errorf("failed to marshal cloud instance state check task: %w", err)
	}

	// Debug log for outgoing message
	slog.Info("Publishing state-check task",
		"queue", queueName,
		"provider", task.Provider,
		"account_id", task.AccountID,
		"region", task.Region,
		"instance_id", task.InstanceID,
		"node_id", task.NodeID,
		"expected_state", task.ExpectedState,
		"action", task.Action,
	)

	err = ch.PublishWithContext(ctx,
		"",        // exchange
		queueName, // routing key (queue name)
		false,     // mandatory
		false,     // immediate
		amqp.Publishing{
			ContentType: "application/json",
			Body:        body,
		},
	)
	if err != nil {
		return fmt.Errorf("failed to publish cloud instance state check task: %w", err)
	}
	return nil
}

// StartStateCheckWorker starts a RabbitMQ consumer that processes cloud instance state check tasks
func StartStateCheckWorker(ch *amqp.Channel, queueName string, queries *db.Queries, logger *slog.Logger) {
	q, err := ch.QueueDeclare(
		queueName, // name
		true,      // durable
		false,     // delete when unused
		false,     // exclusive
		false,     // no-wait
		nil,       // arguments
	)
	if err != nil {
		logger.Error("Failed to declare cloud instance state check queue", "error", err)
		return
	}

	msgs, err := ch.Consume(
		q.Name, // queue
		"",     // consumer
		false,  // auto-ack - we want to ack after processing
		false,  // exclusive
		false,  // no-local
		false,  // no-wait
		nil,    // args
	)
	if err != nil {
		logger.Error("Failed to register cloud instance state check consumer", "error", err)
		return
	}

	go func() {
		for msg := range msgs {
			var task CloudInstanceStateCheckTask
			if err := json.Unmarshal(msg.Body, &task); err != nil {
				logger.Error("Failed to unmarshal state check task", "error", err)
				if nackErr := msg.Nack(false, false); nackErr != nil {
					logger.Error("Failed to nack message", "error", nackErr)
				}
				continue
			}

			logger.Info("Processing cloud instance state check task",
				"node_id", task.NodeID,
				"instance_id", task.InstanceID,
				"action", task.Action)

			err := ProcessStateCheckTask(queries, task, logger)
			if err != nil {
				logger.Error("Error processing cloud instance state check task",
					"node_id", task.NodeID,
					"error", err)
				// Requeue the message for retry (with exponential backoff handled by RabbitMQ)
				if nackErr := msg.Nack(false, true); nackErr != nil {
					logger.Error("Failed to nack message", "error", nackErr)
				}
			} else {
				if ackErr := msg.Ack(false); ackErr != nil {
					logger.Error("Failed to ack message", "error", ackErr)
				}
			}
		}
	}()
}

// ProcessStateCheckTask processes a single state check task
func ProcessStateCheckTask(queries *db.Queries, task CloudInstanceStateCheckTask, logger *slog.Logger) error {
	ctx := context.Background()

	// Add delay to allow AWS state to stabilize
	time.Sleep(30 * time.Second)

	switch task.Provider {
	case "AWS":
		return processAWSStateCheck(queries, task, logger, ctx)
	case "AZURE":
		return processAzureStateCheck(queries, task, logger, ctx)
	default:
		return fmt.Errorf("unsupported provider: %s", task.Provider)
	}
}

// mapAzurePowerStateToEnum converts Azure power state to our enum values
func mapAzurePowerStateToEnum(azureState string) string {
	switch strings.ToLower(azureState) {
	case "running":
		return "running"
	case "stopped":
		return "stopped"
	case "stopping":
		return "stopping"
	case "starting":
		return "pending"
	case "deallocated":
		return "stopped"
	case "deallocating":
		return "stopping"
	default:
		return "unknown"
	}
}

// processAWSStateCheck handles AWS-specific state checking
func processAWSStateCheck(queries *db.Queries, task CloudInstanceStateCheckTask, logger *slog.Logger, ctx context.Context) error {
	// Get AWS client (you'll need to implement this based on your existing AWS client setup)
	client, err := getAWSClientForAccount(task.AccountID, task.Region)
	if err != nil {
		return fmt.Errorf("failed to get AWS client: %w", err)
	}

	// Get actual instance state and IP from AWS
	resp, err := client.DescribeInstances(ctx, &ec2.DescribeInstancesInput{
		InstanceIds: []string{task.InstanceID},
	})
	if err != nil {
		return fmt.Errorf("failed to describe instance: %w", err)
	}

	if len(resp.Reservations) == 0 || len(resp.Reservations[0].Instances) == 0 {
		return fmt.Errorf("instance not found: %s", task.InstanceID)
	}

	instance := resp.Reservations[0].Instances[0]
	actualState := string(instance.State.Name)
	var actualIP string
	if instance.PublicIpAddress != nil {
		actualIP = *instance.PublicIpAddress
	}

	// Update database with actual state
	nodeIDPg, err := converters.StringToPgTypeUUID(task.NodeID)
	if err != nil {
		return fmt.Errorf("failed to convert node ID: %w", err)
	}

	// Update cloud instance state
	err = queries.UpdateCloudInstanceAWSState(ctx, db.UpdateCloudInstanceAWSStateParams{
		CloudInstanceState: db.NullCiStateEnum{CiStateEnum: db.CiStateEnum(actualState), Valid: true},
		CloudInstanceID:    pgtype.Text{String: task.InstanceID, Valid: true},
	})
	if err != nil {
		logger.Error("Failed to update cloud instance state", "error", err)
	}

	// Update IP address if it changed, guarded by node_id + instance_id
	if actualIP != "" {
		if addr, err := netip.ParseAddr(actualIP); err == nil {
			if rows, uerr := queries.UpdateCloudInstanceIPByNodeAndID(ctx, db.UpdateCloudInstanceIPByNodeAndIDParams{
				PublicIpv4Address: &addr,
				NodeID:            *nodeIDPg,
				CloudInstanceID:   pgtype.Text{String: task.InstanceID, Valid: true},
			}); uerr != nil {
				logger.Error("Failed to update IP address (guarded)", "error", uerr)
			} else if rows == 0 {
				logger.Warn("No row matched for guarded IP update", "node_id", task.NodeID, "instance_id", task.InstanceID, "ip", actualIP)
			}
		}
	}

	// Log IP changes with accurate information
	return logIPChange(queries, task, actualIP, logger, ctx)
}

// logIPChange logs IP address changes with accurate state information
func logIPChange(queries *db.Queries, task CloudInstanceStateCheckTask, actualIP string, logger *slog.Logger, ctx context.Context) error {
	userIDPg, err := converters.StringToPgTypeUUID(task.UserID)
	if err != nil {
		return fmt.Errorf("failed to convert user ID: %w", err)
	}

	nodeIDPg, err := converters.StringToPgTypeUUID(task.NodeID)
	if err != nil {
		return fmt.Errorf("failed to convert node ID: %w", err)
	}

	var logMessage string
	switch task.Action {
	case "reip":
		if task.OldIP != "" && actualIP != "" && task.OldIP != actualIP {
			logMessage = fmt.Sprintf("AWS instance REIP completed (%s): IP changed from %s to %s", task.InstanceID, task.OldIP, actualIP)
		} else if actualIP != "" {
			logMessage = fmt.Sprintf("AWS instance REIP completed (%s): IP confirmed as %s", task.InstanceID, actualIP)
		} else {
			logMessage = fmt.Sprintf("AWS instance REIP completed (%s): no IP assigned", task.InstanceID)
		}
	case "start":
		if actualIP != "" {
			logMessage = fmt.Sprintf("AWS instance start completed (%s): IP assigned %s", task.InstanceID, actualIP)
		} else {
			logMessage = fmt.Sprintf("AWS instance start completed (%s): no IP assigned", task.InstanceID)
		}
	default:
		if actualIP != "" {
			logMessage = fmt.Sprintf("AWS instance %s completed (%s): final IP %s", task.Action, task.InstanceID, actualIP)
		} else {
			logMessage = fmt.Sprintf("AWS instance %s completed (%s)", task.Action, task.InstanceID)
		}
	}

	// Use user's preferred timezone for timestamps
	timestamp := utils.GetUserTimestamp(task.UserID)

	err = queries.InsertActivityLog(ctx, db.InsertActivityLogParams{
		Message:   logMessage,
		Type:      db.LogsNodesTypeEnumNODEUPDATE,
		UserID:    *userIDPg,
		NodeID:    *nodeIDPg,
		CreatedAt: timestamp,
	})
	if err != nil {
		return fmt.Errorf("failed to insert activity log: %w", err)
	}

	logger.Info("Logged IP change",
		"node_id", task.NodeID,
		"action", task.Action,
		"old_ip", task.OldIP,
		"new_ip", actualIP)

	return nil
}

// getAWSClientForAccount creates an AWS EC2 client for the given account and region
func getAWSClientForAccount(accountID, region string) (*ec2.Client, error) {
	// Create a new SecretsManager instance using the configured root region, fallback to us-east-1
	sm, err := keys.NewSecretsManager("us-east-1")
	if err != nil {
		return nil, fmt.Errorf("failed to create secrets manager: %w", err)
	}

	// Get credentials from secrets manager
	secretMap, err := sm.GetSecret(accountID)
	if err != nil {
		return nil, fmt.Errorf("failed to get secret for account %s: %w", accountID, err)
	}

	// Extract AWS credentials
	accessKey, ok := secretMap["access_key_id"]
	if !ok {
		return nil, fmt.Errorf("access_key_id not found in secret for account %s", accountID)
	}

	secretKey, ok := secretMap["access_key_secret"]
	if !ok {
		return nil, fmt.Errorf("access_key_secret not found in secret for account %s", accountID)
	}

	// Create AWS config with credentials
	cfg, err := config.LoadDefaultConfig(context.TODO(),
		config.WithRegion(region),
		config.WithCredentialsProvider(credentials.NewStaticCredentialsProvider(accessKey, secretKey, "")),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to load AWS config: %w", err)
	}

	return ec2.NewFromConfig(cfg), nil
}

// processAzureStateCheck handles Azure-specific state checking
func processAzureStateCheck(queries *db.Queries, task CloudInstanceStateCheckTask, logger *slog.Logger, ctx context.Context) error {
	// InstanceID is full Azure resource ID
	parts := strings.Split(task.InstanceID, "/")
	if len(parts) < 9 {
		return fmt.Errorf("invalid Azure instance id: %s", task.InstanceID)
	}
	subscriptionID := parts[2]
	resourceGroupName := parts[4]
	vmName := parts[8]

	// Get tenant/app credentials via AWS Secrets Manager using task.AccountID
	secret, err := azure.GetAzureSecret(task.AccountID)
	if err != nil {
		return fmt.Errorf("failed to get Azure secret for account %s: %w", task.AccountID, err)
	}
	cred, err := azure.GetAzureCredential(secret.TenantID, secret.AppID, secret.AppSecret)
	if err != nil {
		return fmt.Errorf("failed to create Azure credential: %w", err)
	}
	client, err := armcompute.NewVirtualMachinesClient(subscriptionID, cred, nil)
	if err != nil {
		return fmt.Errorf("failed to create Azure compute client: %w", err)
	}

	// Poll instance view until ExpectedState or timeout
	maxAttempts := 30
	var state string
	for attempt := 0; attempt < maxAttempts; attempt++ {
		resp, err := client.InstanceView(ctx, resourceGroupName, vmName, nil)
		if err != nil {
			if strings.Contains(err.Error(), "ResourceNotFound") || strings.Contains(err.Error(), "NotFound") {
				state = "terminated"
				break
			}
			if attempt == 0 {
				return fmt.Errorf("azure InstanceView failed: %w", err)
			}
			break
		}
		powerState := "unknown"
		if resp.Statuses != nil {
			for _, status := range resp.Statuses {
				if status.Code != nil && strings.HasPrefix(*status.Code, "PowerState/") {
					powerState = strings.TrimPrefix(*status.Code, "PowerState/")
					break
				}
			}
		}
		state = mapAzurePowerStateToEnum(powerState)
		if task.ExpectedState == "" || strings.EqualFold(state, task.ExpectedState) {
			break
		}
		time.Sleep(5 * time.Second)
	}

	// Update DB with observed state
	if err := queries.UpdateCloudInstanceAzureState(ctx, db.UpdateCloudInstanceAzureStateParams{
		CloudInstanceState: db.NullCiStateEnum{CiStateEnum: db.CiStateEnum(state), Valid: true},
		CloudInstanceID:    pgtype.Text{String: task.InstanceID, Valid: true},
	}); err != nil {
		logger.Error("Failed to update Azure cloud instance state", "instance_id", task.InstanceID, "error", err)
	}

	// Try to resolve the VM's public IP address via NIC -> PublicIP resource
	var actualIP string
	if strings.EqualFold(state, "running") {
		// List NICs in RG and match by VM ID
		if nicClient2, err := armnetwork.NewInterfacesClient(subscriptionID, cred, nil); err == nil {
			pager := nicClient2.NewListPager(resourceGroupName, nil)
			for pager.More() {
				page, err := pager.NextPage(ctx)
				if err != nil {
					break
				}
				for _, nic := range page.Value {
					if nic.Properties == nil || nic.Properties.VirtualMachine == nil || nic.Properties.VirtualMachine.ID == nil {
						continue
					}
					if strings.EqualFold(*nic.Properties.VirtualMachine.ID, task.InstanceID) {
						// Check IP configurations
						if nic.Properties.IPConfigurations != nil {
							for _, ipconf := range nic.Properties.IPConfigurations {
								if ipconf.Properties != nil && ipconf.Properties.PublicIPAddress != nil && ipconf.Properties.PublicIPAddress.ID != nil {
									pipID := *ipconf.Properties.PublicIPAddress.ID
									pipParts := strings.Split(pipID, "/")
									if len(pipParts) >= 9 {
										pipName := pipParts[len(pipParts)-1]
										pipClient, perr := armnetwork.NewPublicIPAddressesClient(subscriptionID, cred, nil)
										if perr == nil {
											pip, gerr := pipClient.Get(ctx, resourceGroupName, pipName, nil)
											if gerr == nil && pip.Properties != nil && pip.Properties.IPAddress != nil {
												actualIP = *pip.Properties.IPAddress
											}
										}
									}
								}
							}
						}
						break
					}
				}
			}
		}
	}

	// Update guarded IP if we found one
	if actualIP != "" {
		if nodeIDPg, err := converters.StringToPgTypeUUID(task.NodeID); err == nil {
			if addr, perr := netip.ParseAddr(actualIP); perr == nil {
				_, _ = queries.UpdateCloudInstanceIPByNodeAndID(ctx, db.UpdateCloudInstanceIPByNodeAndIDParams{
					PublicIpv4Address: &addr,
					NodeID:            *nodeIDPg,
					CloudInstanceID:   pgtype.Text{String: task.InstanceID, Valid: true},
				})
			}
		}
	}

	// Log IP change info identical to AWS style
	if task.Action == "reip" || task.Action == "start" || task.Action == "stop" || task.Action == "restart" {
		var logMessage string
		if task.Action == "reip" {
			if task.OldIP != "" && actualIP != "" && task.OldIP != actualIP {
				logMessage = fmt.Sprintf("Azure instance REIP completed (%s): IP changed from %s to %s", task.InstanceID, task.OldIP, actualIP)
			} else if actualIP != "" {
				logMessage = fmt.Sprintf("Azure instance REIP completed (%s): IP confirmed as %s", task.InstanceID, actualIP)
			} else {
				logMessage = fmt.Sprintf("Azure instance REIP completed (%s): no IP assigned", task.InstanceID)
			}
		} else {
			if actualIP != "" {
				logMessage = fmt.Sprintf("Azure instance %s completed (%s): final IP %s", task.Action, task.InstanceID, actualIP)
			} else {
				logMessage = fmt.Sprintf("Azure instance %s completed (%s)", task.Action, task.InstanceID)
			}
		}

		if userIDPg, err := converters.StringToPgTypeUUID(task.UserID); err == nil {
			if nodeIDPg, err := converters.StringToPgTypeUUID(task.NodeID); err == nil {
				// Use user's preferred timezone for timestamps
				timestamp := utils.GetUserTimestamp(task.UserID)
				_ = queries.InsertActivityLog(ctx, db.InsertActivityLogParams{
					Message:   logMessage,
					Type:      db.LogsNodesTypeEnumNODEUPDATE,
					UserID:    *userIDPg,
					NodeID:    *nodeIDPg,
					CreatedAt: timestamp,
				})
			}
		}
	}
	return nil
}
