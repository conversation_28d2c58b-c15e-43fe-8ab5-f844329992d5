package awsScheduler

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"log/slog"
	"net/netip"
	"os"
	"strings"
	"sync"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/credentials"
	"github.com/aws/aws-sdk-go-v2/service/ec2"
	"github.com/aws/aws-sdk-go-v2/service/secretsmanager"
	"github.com/jackc/pgx/v5/pgtype"
	amqp "github.com/rabbitmq/amqp091-go"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/converters"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/db"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/utils"
)

// SingleInstanceCheckTask is used to trigger a targeted state/IP verification via the existing queue.
type SingleInstanceCheckTask struct {
	Task          string `json:"task"` // "single_instance_check"
	Provider      string `json:"provider"`
	SecretID      string `json:"secret_id"`            // aws_accounts.id as the secret name
	AccountID     string `json:"account_id,omitempty"` // optional cloud account id
	Region        string `json:"region"`
	InstanceID    string `json:"instance_id"`
	NodeID        string `json:"node_id"`
	Action        string `json:"action"`
	UserID        string `json:"user_id"`
	OldIP         string `json:"old_ip,omitempty"`
	ExpectedState string `json:"expected_state"`
}

// PublishCloudInstanceSyncTask publishes a simple message to trigger the batch sync task.
func PublishCloudInstanceSyncTask(ctx context.Context, ch *amqp.Channel, queueName string) error {
	task := map[string]string{"task": "sync_cloud_instances"}
	body, err := json.Marshal(task)
	if err != nil {
		return fmt.Errorf("failed to marshal cloud instance sync task: %w", err)
	}

	err = ch.PublishWithContext(ctx,
		"",        // exchange
		queueName, // routing key (queue name)
		false,     // mandatory
		false,     // immediate
		amqp.Publishing{
			ContentType: "application/json",
			Body:        body,
		},
	)
	if err != nil {
		return fmt.Errorf("failed to publish cloud instance sync task: %w", err)
	}
	return nil
}

// PublishSingleInstanceCheckTask publishes a targeted state/IP verification task.
func PublishSingleInstanceCheckTask(ctx context.Context, ch *amqp.Channel, queueName string, task SingleInstanceCheckTask) error {
	if task.Task == "" {
		task.Task = "single_instance_check"
	}
	body, err := json.Marshal(task)
	if err != nil {
		return fmt.Errorf("failed to marshal single instance check task: %w", err)
	}
	// Debug publish log
	slog.Info("Publishing single-instance check",
		"queue", queueName,
		"provider", task.Provider,
		"account_id", task.AccountID,
		"secret_id", task.SecretID,
		"region", task.Region,
		"instance_id", task.InstanceID,
		"node_id", task.NodeID,
		"expected_state", task.ExpectedState,
		"action", task.Action,
	)
	return ch.PublishWithContext(ctx, "", queueName, false, false, amqp.Publishing{
		ContentType: "application/json",
		Body:        body,
	})
}

// StartCloudInstanceSyncWorker starts a RabbitMQ consumer that listens for cloud instance sync tasks.
func StartCloudInstanceSyncWorker(ch *amqp.Channel, queueName string, queries *db.Queries, logger *slog.Logger) {
	q, err := ch.QueueDeclare(
		queueName, // name
		true,      // durable
		false,     // delete when unused
		false,     // exclusive
		false,     // no-wait
		nil,       // arguments
	)
	if err != nil {
		logger.Error("Failed to declare cloud instance sync queue", "error", err)
		return
	}
	logger.Info("Declared queue for cloud instance sync", "queue", q.Name)

	msgs, err := ch.Consume(
		q.Name, // queue
		"",     // consumer
		false,  // auto-ack - we want to ack after processing
		false,  // exclusive
		false,  // no-local
		false,  // no-wait
		nil,    // args
	)
	if err != nil {
		logger.Error("Failed to register cloud instance sync consumer", "error", err)
		return
	}
	logger.Info("Registered consumer for cloud instance sync", "queue", q.Name)

	go func() {
		for msg := range msgs {
			// Detect envelope type
			var envelope struct {
				Task string `json:"task"`
			}
			_ = json.Unmarshal(msg.Body, &envelope)
			if envelope.Task == "single_instance_check" {
				var single SingleInstanceCheckTask
				if err := json.Unmarshal(msg.Body, &single); err != nil {
					logger.Error("Failed to parse single instance check task", "error", err)
					if nackErr := msg.Nack(false, false); nackErr != nil {
						logger.Error("Failed to nack message", "error", nackErr)
					}
					continue
				}
				logger.Info("Received single-instance check",
					"provider", single.Provider,
					"account_id", single.AccountID,
					"secret_id", single.SecretID,
					"region", single.Region,
					"instance_id", single.InstanceID,
					"node_id", single.NodeID,
					"expected_state", single.ExpectedState,
					"action", single.Action,
				)
				if err := processSingleInstanceCheck(queries, single, logger); err != nil {
					logger.Error("Error processing single instance check", "error", err)
					if nackErr := msg.Nack(false, true); nackErr != nil {
						logger.Error("Failed to nack message", "error", nackErr)
					}
					continue
				}
				if ackErr := msg.Ack(false); ackErr != nil {
					logger.Error("Failed to ack message", "error", ackErr)
				}
				continue
			}

			// Default: batch sync
			logger.Info("Received cloud instance sync task")
			if err := SyncCloudInstanceState(queries, context.Background()); err != nil {
				logger.Error("Error syncing cloud instance state", "error", err)
			}
			if err := msg.Ack(false); err != nil {
				logger.Error("Failed to acknowledge cloud instance sync message", "error", err)
			}
		}
	}()
}

type SubaccountCredentials struct {
	AccessKey string `json:"access_key_id"`
	SecretKey string `json:"access_key_secret"`
}

func getSecret(secretName string) (*SubaccountCredentials, error) {
	// Try multiple ways to load AWS config to avoid region-related failures
	regionsToTry := []string{os.Getenv("AWS_ROOT_REGION"), os.Getenv("AWS_REGION")}
	var cfg aws.Config
	var err error
	for _, r := range regionsToTry {
		if r == "" {
			continue
		}
		cfg, err = config.LoadDefaultConfig(context.TODO(), config.WithRegion(r))
		if err == nil {
			break
		}
	}
	if err != nil {
		// Last resort: try without explicit region
		cfg, err = config.LoadDefaultConfig(context.TODO())
		if err != nil {
			return nil, fmt.Errorf("failed to load AWS SDK config (no region available): %w", err)
		}
	}

	client := secretsmanager.NewFromConfig(cfg)
	result, err := client.GetSecretValue(context.TODO(), &secretsmanager.GetSecretValueInput{
		SecretId: aws.String(secretName),
	})
	if err != nil {
		return nil, fmt.Errorf("failed to retrieve secret '%s': %w", secretName, err)
	}

	var creds SubaccountCredentials
	if err := json.Unmarshal([]byte(*result.SecretString), &creds); err != nil {
		return nil, fmt.Errorf("failed to parse secret JSON for '%s': %w", secretName, err)
	}
	return &creds, nil
}

func getEC2Client(accessKey, secretKey, region string) (*ec2.Client, error) {
	cfg, err := config.LoadDefaultConfig(context.TODO(),
		config.WithCredentialsProvider(credentials.NewStaticCredentialsProvider(accessKey, secretKey, "")),
		config.WithRegion(region),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create AWS config: %w", err)
	}
	return ec2.NewFromConfig(cfg), nil
}

func getInstanceStates(queries *db.Queries, client *ec2.Client, instanceIDs []string, accountID string) {
	if len(instanceIDs) == 0 {
		log.Printf("[Account: %s] No instances to process.", accountID)
		return
	}

	log.Printf("[Account: %s] Processing up to %d instance IDs", accountID, len(instanceIDs))

	foundInstances := make(map[string]bool)

	batchSize := 10
	for i := 0; i < len(instanceIDs); i += batchSize {
		end := i + batchSize
		if end > len(instanceIDs) {
			end = len(instanceIDs)
		}
		batch := instanceIDs[i:end]

		resp, err := client.DescribeInstances(context.TODO(), &ec2.DescribeInstancesInput{
			InstanceIds: batch,
		})
		if err != nil && strings.Contains(err.Error(), "InvalidInstanceID.NotFound") {
			log.Printf("[Account: %s] Batch had deleted instances, retrying individually...", accountID)
			for _, id := range batch {
				resp, err := client.DescribeInstances(context.TODO(), &ec2.DescribeInstancesInput{
					InstanceIds: []string{id},
				})
				if err != nil && strings.Contains(err.Error(), "InvalidInstanceID.NotFound") {
					log.Printf("[Account: %s] Instance %s no longer exists, marking as terminated.", accountID, id)
					markTerminated(queries, accountID, id)
					continue
				}
				processAWSResponse(resp, queries, accountID, foundInstances)
			}
			continue
		}

		if err != nil {
			log.Printf("[Account: %s] Unexpected DescribeInstances error: %v", accountID, err)
			continue
		}

		processAWSResponse(resp, queries, accountID, foundInstances)
	}

	// Handle any missing instance IDs - happens only if the syncing process has not been run for that long
	// that AWS deleted data about the terminated instance completely - the deleting of data seems to happen several days after
	// the real termination
	for _, id := range instanceIDs {
		if !foundInstances[id] {
			log.Printf("[Account: %s] Instance %s missing from AWS response, marking as terminated.", accountID, id)
			markTerminated(queries, accountID, id)
		}
	}
}

func processAWSResponse(resp *ec2.DescribeInstancesOutput, queries *db.Queries, accountID string, found map[string]bool) {
	for _, reservation := range resp.Reservations {
		for _, instance := range reservation.Instances {
			instanceID := *instance.InstanceId
			state := string(instance.State.Name)

			found[instanceID] = true
			log.Printf("[Account: %s] Instance %s is in state: %s", accountID, instanceID, state)

			updateParams := db.UpdateCloudInstanceAWSStateParams{
				CloudInstanceState: db.NullCiStateEnum{CiStateEnum: db.CiStateEnum(state), Valid: true},
				CloudInstanceID:    pgtype.Text{String: instanceID, Valid: true},
			}

			if err := queries.UpdateCloudInstanceAWSState(context.TODO(), updateParams); err != nil {
				log.Printf("Error updating state for %s: %v", instanceID, err)
			}
		}
	}
}

func markTerminated(queries *db.Queries, accountID, instanceID string) {
	updateParams := db.UpdateCloudInstanceAWSStateParams{
		CloudInstanceState: db.NullCiStateEnum{CiStateEnum: db.CiStateEnum("terminated"), Valid: true},
		CloudInstanceID:    pgtype.Text{String: instanceID, Valid: true},
	}
	if err := queries.UpdateCloudInstanceAWSState(context.TODO(), updateParams); err != nil {
		log.Printf("Error marking instance %s as terminated: %v", instanceID, err)
	}
}

// process Cloud Instances in parallel
func SyncCloudInstanceState(queries *db.Queries, ctx context.Context) error {
	log.Println("Syncing AWS cloud instances started")

	// Mark instances as 'failed' if they have no cloud_instance_id even after 10 min after deployemnt
	err := queries.MarkStuckInstancesAsFailed(ctx)
	if err != nil {
		log.Printf("Failed to mark stuck instances as 'failed': %v", err)
	} else {
		log.Println("Stuck instances mark step done")
	}

	// Mark instances as 'terminated' if their AWS accounts are inactive
	err = queries.MarkInstancesAsTerminatedForInactiveAccounts(ctx)
	if err != nil {
		log.Printf("Failed to mark instances as 'terminated' for inactive accounts: %v", err)
	} else {
		log.Println("Inactive accounts instances sync step done")
	}

	// Get instances whose status should be checked for updates
	instances, err := queries.GetCloudInstancesWithRegionAndSecretNameForAccount(ctx)

	if err != nil {
		log.Printf("Error fetching updatable instances: %v", err)
		return nil
	}

	// If nothing validates for updates (no cloud instance id, no ACTIVE aws account, already terminated), stop update here
	if len(instances) == 0 {
		log.Println("Nothing to sync with aws, all instances states final. Stopping sync process.")
		return nil
	}

	// Create a nested map: account → region → instances that returns:
	// map[************:map[eu-west-1:[i-034295fe21c3bebf7] eu-west-2:[i-007b97bf88c997c6a]]]
	accountInstancesMap := make(map[string]map[string][]string)
	secretMap := make(map[string]string)

	for _, instance := range instances {
		accountID := instance.CloudAccountID.String

		if accountInstancesMap[accountID] == nil {
			accountInstancesMap[accountID] = make(map[string][]string)
		}
		accountInstancesMap[accountID][instance.Region] = append(accountInstancesMap[accountID][instance.Region], instance.CloudInstanceID.String)
		secretMap[accountID] = instance.SecretID.String()
	}

	// Use Goroutines to process accounts in parallel
	var wg sync.WaitGroup

	for accountID, regions := range accountInstancesMap {

		wg.Add(1)
		go func(accountID string, regions map[string][]string) {
			defer wg.Done()

			secretName := secretMap[accountID]
			creds, err := getSecret(secretName)
			if err != nil {
				log.Printf("[Account: %s] Skipping sync due to secret retrieval failure: %v", accountID, err)
				return
			}
			log.Printf("[Account: %s] Secret retrieved successfully!", accountID)

			// Process each region
			for region, instanceIDs := range regions {
				log.Printf("[Account: %s] [Region: %s] Processing region with instances: %v", accountID, region, instanceIDs)

				wg.Add(1)
				go func(accountID, region string, instanceIDs []string) {
					defer wg.Done()

					client, err := getEC2Client(creds.AccessKey, creds.SecretKey, region)
					if err != nil {
						log.Printf("[Account: %s] [Region: %s] Skipping region due to AWS client error: %v", accountID, region, err)
						return
					}
					log.Printf("[Account: %s] [Region: %s] EC2 client initialized", accountID, region)
					getInstanceStates(queries, client, instanceIDs, accountID)
					log.Printf("[Account: %s] [Region: %s] Instance state retrieval completed.", accountID, region)
				}(accountID, region, instanceIDs)
			}
		}(accountID, regions)
	}
	wg.Wait()

	log.Println("Instances sync process done")

	return nil
}

// processSingleInstanceCheck handles a targeted instance verification and logs IP/state accurately.
func processSingleInstanceCheck(queries *db.Queries, task SingleInstanceCheckTask, logger *slog.Logger) error {
	if strings.ToUpper(task.Provider) != "AWS" {
		return fmt.Errorf("unsupported provider: %s", task.Provider)
	}
	// Get subaccount credentials using aws_accounts.id as secret name
	creds, err := getSecret(task.SecretID)
	if err != nil {
		return fmt.Errorf("failed to get secret %s: %w", task.SecretID, err)
	}
	client, err := getEC2Client(creds.AccessKey, creds.SecretKey, task.Region)
	if err != nil {
		return fmt.Errorf("failed to init ec2 client: %w", err)
	}

	// Poll until expected state is reached (best-effort), with a timeout
	var state string
	var newIP string
	maxAttempts := 30
	for attempt := 0; attempt < maxAttempts; attempt++ {
		resp, err := client.DescribeInstances(context.TODO(), &ec2.DescribeInstancesInput{InstanceIds: []string{task.InstanceID}})
		if err != nil {
			// Treat InvalidInstanceID.NotFound as terminated
			if strings.Contains(strings.ToLower(err.Error()), "invalidinstanceid.notfound") {
				state = "terminated"
				newIP = ""
				logger.Info("DescribeInstances returned NotFound; treating as terminated",
					"instance_id", task.InstanceID,
				)
				break
			}
			if attempt == 0 { // if first call fails on first attempt, return
				return fmt.Errorf("describe instances failed: %w", err)
			}
			// On later attempts, stop polling and proceed with last known state
			break
		}
		if len(resp.Reservations) == 0 || len(resp.Reservations[0].Instances) == 0 {
			// Instance no longer returned by AWS; treat as terminated
			state = "terminated"
			newIP = ""
			logger.Info("Instance missing in DescribeInstances; treating as terminated",
				"instance_id", task.InstanceID,
			)
			break
		}
		ins := resp.Reservations[0].Instances[0]
		state = string(ins.State.Name)
		if ins.PublicIpAddress != nil {
			newIP = *ins.PublicIpAddress
		} else {
			newIP = ""
		}

		if task.ExpectedState == "" || strings.EqualFold(state, task.ExpectedState) {
			break
		}
		time.Sleep(5 * time.Second)
	}

	logger.Info("Observed AWS state",
		"account_id", task.AccountID,
		"region", task.Region,
		"instance_id", task.InstanceID,
		"state", state,
		"ip", newIP,
	)

	// Update DB state
	// Try guarded by aws_account_id + instance_id first; fallback to node_id + instance_id
	var updated bool
	if acc := task.AccountID; acc != "" {
		if accIDPg, err := converters.StringToPgTypeUUID(acc); err == nil {
			if rows, err := queries.UpdateCloudInstanceAWSStateByAwsAccountAndID(context.TODO(), db.UpdateCloudInstanceAWSStateByAwsAccountAndIDParams{
				CloudInstanceState: db.NullCiStateEnum{CiStateEnum: db.CiStateEnum(state), Valid: true},
				AwsAccountID:       *accIDPg,
				CloudInstanceID:    pgtype.Text{String: task.InstanceID, Valid: true},
			}); err != nil {
				logger.Error("Failed guarded state update by account", "aws_account_id", acc, "instance", task.InstanceID, "error", err)
			} else {
				updated = rows > 0
				logger.Info("Guarded update by account result", "aws_account_id", acc, "instance_id", task.InstanceID, "updated", updated)
			}
		} else {
			logger.Error("Invalid aws_account_id for state update", "aws_account_id", acc, "error", err)
		}
	}
	if !updated {
		if nodeIDPg, err := converters.StringToPgTypeUUID(task.NodeID); err == nil {
			if rows, err := queries.UpdateCloudInstanceAWSStateByNodeAndID(context.TODO(), db.UpdateCloudInstanceAWSStateByNodeAndIDParams{
				CloudInstanceState: db.NullCiStateEnum{CiStateEnum: db.CiStateEnum(state), Valid: true},
				NodeID:             *nodeIDPg,
				CloudInstanceID:    pgtype.Text{String: task.InstanceID, Valid: true},
			}); err != nil {
				logger.Error("Failed guarded state update by node", "node_id", task.NodeID, "instance", task.InstanceID, "error", err)
			} else {
				updated = rows > 0
				logger.Info("Guarded update by node result", "node_id", task.NodeID, "instance_id", task.InstanceID, "updated", updated)
			}
		} else {
			logger.Error("Invalid node id for state update", "node_id", task.NodeID, "error", err)
		}
	}
	if !updated {
		logger.Warn("No matching row updated for state change",
			"account_id", task.AccountID,
			"node_id", task.NodeID,
			"instance_id", task.InstanceID,
			"state", state,
		)
	}

	// Update public IP guarded by node_id + instance_id so we never lose track across retries
	if newIP != "" {
		if nodeIDPg, err := converters.StringToPgTypeUUID(task.NodeID); err == nil {
			if addr, perr := netip.ParseAddr(newIP); perr == nil {
				if rows, uerr := queries.UpdateCloudInstanceIPByNodeAndID(context.TODO(), db.UpdateCloudInstanceIPByNodeAndIDParams{
					PublicIpv4Address: &addr,
					NodeID:            *nodeIDPg,
					CloudInstanceID:   pgtype.Text{String: task.InstanceID, Valid: true},
				}); uerr != nil {
					logger.Error("Failed to update public IP (guarded)", "error", uerr)
				} else if rows == 0 {
					logger.Warn("No row matched for guarded IP update",
						"node_id", task.NodeID,
						"instance_id", task.InstanceID,
						"ip", newIP,
					)
				} else {
					logger.Info("Updated public IP (guarded)", "node_id", task.NodeID, "instance_id", task.InstanceID, "ip", newIP)
				}
			}
		}
	}

	// Build accurate activity log
	userIDPg, err := converters.StringToPgTypeUUID(task.UserID)
	if err != nil {
		return fmt.Errorf("invalid user id: %w", err)
	}
	nodeIDPg, err := converters.StringToPgTypeUUID(task.NodeID)
	if err != nil {
		return fmt.Errorf("invalid node id: %w", err)
	}
	ts := utils.GetUserTimestamp(task.UserID)

	var msg string
	switch strings.ToLower(task.Action) {
	case "reip":
		if task.OldIP != "" && newIP != "" && task.OldIP != newIP {
			msg = fmt.Sprintf("AWS instance REIP completed (%s): IP changed from %s to %s", task.InstanceID, task.OldIP, newIP)
		} else if newIP != "" {
			msg = fmt.Sprintf("AWS instance REIP completed (%s): IP confirmed as %s", task.InstanceID, newIP)
		} else {
			msg = fmt.Sprintf("AWS instance REIP completed (%s): no IP assigned", task.InstanceID)
		}
	case "start":
		if newIP != "" {
			msg = fmt.Sprintf("AWS instance start completed (%s): IP assigned %s", task.InstanceID, newIP)
		} else {
			msg = fmt.Sprintf("AWS instance start completed (%s): no IP assigned", task.InstanceID)
		}
	case "stop":
		msg = fmt.Sprintf("AWS instance stop completed (%s): state %s", task.InstanceID, state)
	case "reboot":
		msg = fmt.Sprintf("AWS instance reboot completed (%s): state %s", task.InstanceID, state)
	case "terminate":
		msg = fmt.Sprintf("AWS instance terminate completed (%s): state %s", task.InstanceID, state)
	default:
		msg = fmt.Sprintf("AWS instance %s completed (%s): state %s", task.Action, task.InstanceID, state)
	}

	// Insert activity log; include IP if we have a newIP
	if newIP != "" {
		if addr, perr := netip.ParseAddr(newIP); perr == nil {
			if _, execErr := queries.ExecRaw(context.TODO(), "INSERT INTO logs_nodes (message, type, user_id, node_id, created_at, ip_address) VALUES ($1, $2, $3, $4, $5, $6)", msg, db.LogsNodesTypeEnumNODEUPDATE, *userIDPg, *nodeIDPg, ts, addr); execErr != nil {
				logger.Error("Failed to insert activity log with IP", "error", execErr)
			}
		} else {
			logger.Warn("Failed to parse newIP for logging", "ip", newIP)
		}
	} else {
		if err := queries.InsertActivityLog(context.TODO(), db.InsertActivityLogParams{
			Message:   msg,
			Type:      db.LogsNodesTypeEnumNODEUPDATE,
			UserID:    *userIDPg,
			NodeID:    *nodeIDPg,
			CreatedAt: ts,
		}); err != nil {
			logger.Error("Failed to insert activity log", "error", err)
		}
	}
	return nil
}
