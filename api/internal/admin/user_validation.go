package admin

import (
	"context"
	"fmt"

	"github.com/jackc/pgx/v5/pgtype"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/converters"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/db"
	"golang.org/x/crypto/ssh"
)

// ValidateAndFilterUsersWithSSHAndUsername validates that users have both SSH key and username
// Returns only the user IDs that have valid SSH keys and usernames
func ValidateAndFilterUsersWithSSHAndUsername(queries *db.Queries, userIDs []string) ([]string, error) {
	if len(userIDs) == 0 {
		return userIDs, nil
	}

	// Convert string IDs to pgtype.UUID
	userIDsPgType := make([]pgtype.UUID, 0, len(userIDs))
	for _, userID := range userIDs {
		userIDPgType, err := converters.StringToPgTypeUUID(userID)
		if err != nil {
			return nil, fmt.Errorf("invalid user ID format: %s", userID)
		}
		userIDsPgType = append(userIDsPgType, *userIDPgType)
	}

	// Get users from database
	users, err := queries.GetUsersFromIDs(context.Background(), userIDsPgType)
	if err != nil {
		return nil, fmt.Errorf("failed to get users from IDs: %v", err)
	}

	// Filter users that have both valid SSH key and username
	validUserIDs := make([]string, 0)
	for _, user := range users {
		hasValidSSHKey := false
		hasValidUsername := false

		// Check SSH key validity
		if user.SshKey.Valid && len(user.SshKey.String) > 0 {
			_, _, _, _, err := ssh.ParseAuthorizedKey([]byte(user.SshKey.String))
			if err == nil {
				hasValidSSHKey = true
			}
		}

		// Check username validity (custom_username must be set)
		if user.CustomUsername.Valid && len(user.CustomUsername.String) > 0 {
			hasValidUsername = true
		}

		// Only include users that have both valid SSH key and username
		if hasValidSSHKey && hasValidUsername {
			userIDString, err := converters.PgTypeUUIDToString(user.ID)
			if err != nil {
				return nil, fmt.Errorf("failed to convert user ID to string: %v", err)
			}
			validUserIDs = append(validUserIDs, *userIDString)
		}
	}

	return validUserIDs, nil
}
