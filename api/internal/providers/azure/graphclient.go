package azure

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
)

type GraphClient struct {
	clientID     string
	clientSecret string
	tenantID     string
}
type GetGroupMembersResponse struct {
	OdataContext string `json:"@odata.context"`
	Value        []struct {
		OdataType         string `json:"@odata.type"`
		ID                string `json:"id"`
		BusinessPhones    []any  `json:"businessPhones"`
		DisplayName       string `json:"displayName"`
		GivenName         string `json:"givenName"`
		JobTitle          string `json:"jobTitle"`
		Mail              string `json:"mail"`
		MobilePhone       string `json:"mobilePhone"`
		OfficeLocation    string `json:"officeLocation"`
		PreferredLanguage string `json:"preferredLanguage"`
		Surname           string `json:"surname"`
		UserPrincipalName string `json:"userPrincipalName"`
		AccountEnabled    bool   `json:"accountEnabled"`
	} `json:"value"`
}

type GroupMember struct {
	ID             string `json:"id" format:"uuid"`
	FirstName      string `json:"first_name"`
	LastName       string `json:"last_name"`
	Username       string `json:"username" format:"email"`
	Role           string `json:"role" enum:"standard,admin"`
	AccountEnabled bool   `json:"accountEnabled"`
}

func NewGraphClient(clientID string, clientSecret string, tenantID string) *GraphClient {
	return &GraphClient{
		clientID:     clientID,
		clientSecret: clientSecret,
		tenantID:     tenantID,
	}
}

func (c *GraphClient) GetAccessToken() (*string, error) {
	tokenURL := fmt.Sprintf("https://login.microsoftonline.com/%s/oauth2/v2.0/token", c.tenantID)
	data := url.Values{}
	data.Set("grant_type", "client_credentials")
	data.Set("client_id", c.clientID)
	data.Set("client_secret", c.clientSecret)
	data.Set("scope", "https://graph.microsoft.com/.default")

	req, err := http.NewRequest("POST", tokenURL, bytes.NewBufferString(data.Encode()))
	if err != nil {
		return nil, err
	}
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("failed to get token, status: %s, body: %s", resp.Status, string(body))
	}

	var tokenResponse struct {
		AccessToken string `json:"access_token"`
	}
	if err := json.Unmarshal(body, &tokenResponse); err != nil {
		return nil, err
	}

	return &tokenResponse.AccessToken, nil
}

// GetGroupMembers fetches the members of a given Azure AD group
func (c *GraphClient) GetGroupMembers(accessToken string, groupID string, roleTag string) ([]GroupMember, error) {
	groupMembers := make([]GroupMember, 0)
	if accessToken == "" {
		return groupMembers, fmt.Errorf("no access token available, please authenticate first")
	}

	graphURL := fmt.Sprintf("https://graph.microsoft.com/v1.0/groups/%s/members", groupID)
	req, err := http.NewRequest("GET", graphURL, nil)
	if err != nil {
		return groupMembers, err
	}
	req.Header.Set("Authorization", "Bearer "+accessToken)

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return groupMembers, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	if resp.StatusCode != http.StatusOK {
		return groupMembers, fmt.Errorf("failed to get group members, status: %s, body: %s", resp.Status, string(body))
	}

	getGroupMembersResponse := GetGroupMembersResponse{}

	err = json.Unmarshal(body, &getGroupMembersResponse)
	if err != nil {
		return groupMembers, err
	}

	for _, groupMemberResponse := range getGroupMembersResponse.Value {
		groupMembers = append(groupMembers, GroupMember{
			ID:             groupMemberResponse.ID,
			FirstName:      groupMemberResponse.GivenName,
			LastName:       groupMemberResponse.Surname,
			Username:       groupMemberResponse.UserPrincipalName,
			Role:           roleTag,
			AccountEnabled: groupMemberResponse.AccountEnabled,
		})
	}

	return groupMembers, nil
}
