package azure

import (
	"context"
	"encoding/json"
	"fmt"
	"os"

	"github.com/Azure/azure-sdk-for-go/sdk/azidentity"
	"github.com/Azure/azure-sdk-for-go/sdk/resourcemanager/resources/armresources"
	"github.com/Azure/azure-sdk-for-go/sdk/resourcemanager/resources/armsubscriptions"
	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/secretsmanager"
)

// GetAzureCredential creates a credential from secrets.
func GetAzureCredential(tenantID, clientID, clientSecret string) (*azidentity.ClientSecretCredential, error) {
	cred, err := azidentity.NewClientSecretCredential(tenantID, clientID, clientSecret, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create Azure credential: %w", err)
	}
	return cred, nil
}

// NewSubscriptionClient returns a subscriptions client
func NewSubscriptionClient(cred *azidentity.ClientSecretCredential) (*armsubscriptions.Client, error) {
	return armsubscriptions.NewClient(cred, nil)
}

// NewResourceClient returns a resource client (for e.g. locations)
func NewResourceClient(cred *azidentity.ClientSecretCredential, subscriptionID string) (*armresources.Client, error) {
	return armresources.NewClient(subscriptionID, cred, nil)
}

type SubaccountCredentials struct {
	TenantID       string `query:"tenantId,required" doc:"Azure Tenant ID"`
	AppID          string `query:"AppId,required" doc:"Azure App ID"`
	AppSecret      string `query:"AppSecret,required" doc:"Azure App Secret"`
	SubscriptionID string `query:"SubscriptionId,required" doc:"Azure Subscription ID"`
}

func GetAzureSecret(secretName string) (*SubaccountCredentials, error) {
	// Try env region first, then fall back to us-east-1 to be resilient
	regions := []string{}
	if envRegion := os.Getenv("AWS_ROOT_REGION"); envRegion != "" {
		regions = append(regions, envRegion)
	}
	regions = append(regions, "us-east-1")

	var lastErr error
	for _, region := range regions {
		cfg, err := config.LoadDefaultConfig(context.TODO(), config.WithRegion(region))
		if err != nil {
			lastErr = fmt.Errorf("failed to load AWS SDK config (%s): %w", region, err)
			continue
		}

		client := secretsmanager.NewFromConfig(cfg)
		result, err := client.GetSecretValue(context.TODO(), &secretsmanager.GetSecretValueInput{
			SecretId: aws.String(secretName),
		})
		if err != nil {
			lastErr = fmt.Errorf("failed to retrieve secret in %s: %w", region, err)
			continue
		}

		var creds SubaccountCredentials
		if err := json.Unmarshal([]byte(*result.SecretString), &creds); err != nil {
			lastErr = fmt.Errorf("failed to parse secret JSON: %w", err)
			continue
		}
		return &creds, nil
	}
	if lastErr == nil {
		lastErr = fmt.Errorf("failed to retrieve secret: no region succeeded")
	}
	return nil, lastErr
}
