package activitylogs

import (
	"context"
	"net/netip"
	"time"

	"github.com/jackc/pgx/v5/pgtype"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/db"
)

// InsertLogWithIP inserts a node activity log and optionally stores the IP address (for cloud instance actions).
func InsertLogWithIP(queries *db.Queries, message string, logType db.LogsNodesTypeEnum, userId pgtype.UUID, nodeId pgtype.UUID, timestamp pgtype.Timestamp, ip *netip.Addr) error {
	if ip == nil {
		return queries.InsertActivityLog(context.Background(), db.InsertActivityLogParams{
			Message:   message,
			Type:      logType,
			UserID:    userId,
			NodeID:    nodeId,
			CreatedAt: timestamp,
		})
	}
	// Use the generated query to insert with IP
	return queries.InsertActivityLogWithIP(context.Background(), db.InsertActivityLogWithIPParams{
		Message:   message,
		Type:      logType,
		UserID:    userId,
		NodeID:    nodeId,
		CreatedAt: timestamp,
		IpAddress: ip,
	})
}

// IPHistoryEntry represents a historical IP address assignment for a node
type IPHistoryEntry struct {
	IPAddress string    `json:"ip_address"`
	CreatedAt time.Time `json:"created_at"`
}

// GetNodeIPHistory returns the list of IP assignments from logs_nodes for a node (most recent first)
func GetNodeIPHistory(queries *db.Queries, nodeId pgtype.UUID) ([]IPHistoryEntry, error) {
	rows, err := queries.GetNodeIPHistory(context.Background(), nodeId)
	if err != nil {
		return nil, err
	}

	var history []IPHistoryEntry
	for _, row := range rows {
		entry := IPHistoryEntry{
			IPAddress: row.IpAddress,
			CreatedAt: row.CreatedAt.Time,
		}
		history = append(history, entry)
	}
	return history, nil
}
