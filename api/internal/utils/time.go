package utils

import (
	"os"
	"time"

	"github.com/jackc/pgx/v5/pgtype"
)

// GetTimestampInTimezone returns a pgtype.Timestamp with the current time in the specified timezone
func GetTimestampInTimezone(timezone string) pgtype.Timestamp {
	location, err := time.LoadLocation(timezone)
	if err != nil {
		// Fallback to UTC if timezone loading fails
		return pgtype.Timestamp{Time: time.Now().UTC(), Valid: true}
	}

	return pgtype.Timestamp{
		Time:  time.Now().In(location),
		Valid: true,
	}
}

// GetCETTimestamp returns a pgtype.Timestamp with the current time in CET timezone
// This is a convenience function for European users
func GetCETTimestamp() pgtype.Timestamp {
	return GetTimestampInTimezone("Europe/Berlin")
}

// GetUTCTimestamp returns a pgtype.Timestamp with the current time in UTC
func GetUTCTimestamp() pgtype.Timestamp {
	return pgtype.Timestamp{Time: time.Now().UTC(), Valid: true}
}

// Common timezone convenience functions
func GetESTTimestamp() pgtype.Timestamp {
	return GetTimestampInTimezone("America/New_York")
}

func GetPSTTimestamp() pgtype.Timestamp {
	return GetTimestampInTimezone("America/Los_Angeles")
}

func GetCSTTimestamp() pgtype.Timestamp {
	return GetTimestampInTimezone("America/Chicago")
}

// GetUserTimestamp returns a timestamp in the user's preferred timezone
// For now, this can be configured via environment variable or defaults to UTC
// TODO: In the future, this could read from user preferences in the database
func GetUserTimestamp(userID string) pgtype.Timestamp {
	// Strategy 1: Check environment variable for default timezone
	if defaultTZ := os.Getenv("DEFAULT_USER_TIMEZONE"); defaultTZ != "" {
		return GetTimestampInTimezone(defaultTZ)
	}

	// Strategy 2: Could check user preferences from database (future enhancement)
	// userTimezone := getUserTimezoneFromDB(userID)
	// if userTimezone != "" {
	//     return GetTimestampInTimezone(userTimezone)
	// }

	// Strategy 3: Default fallback to UTC
	return GetUTCTimestamp()
}

// GetCETTime returns the current time in CET timezone
func GetCETTime() time.Time {
	cetLocation, err := time.LoadLocation("Europe/Berlin")
	if err != nil {
		// Fallback to UTC if CET timezone loading fails
		return time.Now().UTC()
	}

	return time.Now().In(cetLocation)
}
