package users

import (
	"context"
	"fmt"

	"github.com/jackc/pgx/v5/pgtype"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/converters"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/db"
	"golang.org/x/crypto/ssh"
)

func SetUserSshKey(queries *db.Queries, userID string, sshKey string, sshKeyLabel string) (*User, error) {
	userIDPgType, err := converters.StringToPgTypeUUID(userID)
	if err != nil {
		return nil, err
	}

	if _, _, _, _, err = ssh.ParseAuthorizedKey([]byte(sshKey)); err != nil {
		return nil, fmt.Errorf("invalid public SSH key")
	}

	updatedUser, err := queries.SetUserSshKey(context.Background(), db.SetUserSshKeyParams{
		SshKey:      pgtype.Text{String: sshKey, Valid: true},
		SshKeyLabel: pgtype.Text{String: sshKeyLabel, Valid: true},
		ID:          *userIDPgType,
	})
	if err != nil {
		return nil, err
	}

	userResponse := User{
		ID:                 userID,
		FullName:           updatedUser.FullName.String,
		Username:           updatedUser.Username,
		SSHKey:             updatedUser.SshKey.String,
		SSHKeyLabel:        updatedUser.SshKeyLabel.String,
		SSHKeyCreationDate: updatedUser.SshKeyCreationDate.Time,
	}

	return &userResponse, nil
}

func RemoveUserSshKey(queries *db.Queries, userID string) error {
	userIDPgType, err := converters.StringToPgTypeUUID(userID)
	if err != nil {
		return err
	}

	err = queries.RemoveUserSshKey(context.Background(), *userIDPgType)

	if err != nil {
		return fmt.Errorf("could not remove user's public SSH key: %e", err)
	}
	return nil
}
