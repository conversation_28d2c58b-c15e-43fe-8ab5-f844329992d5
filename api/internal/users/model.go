package users

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/jackc/pgx/v5/pgtype"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/converters"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/db"
	"golang.org/x/crypto/ssh"
)

type User struct {
	ID                  string    `json:"id" format:"uuid"`
	FullName            string    `json:"full_name"`
	Username            string    `json:"username"`
	CustomUsername      string    `json:"custom_username"`
	SSHKey              string    `json:"ssh_key"`
	SSHKeyLabel         string    `json:"ssh_key_label"`
	SSHKeyCreationDate  time.Time `json:"ssh_key_creation_date"`
	ValidCustomUsername bool      `json:"valid_custom_username"`
	ValidSshKey         bool      `json:"valid_ssh_key"`
	AppRole             string    `json:"app_role"`
}

type Script struct {
	ID          string            `json:"id" format:"uuid"`
	Name        string            `json:"name"`
	Description string            `json:"description"`
	Content     string            `json:"content"`
	ScriptType  db.ScriptTypeEnum `json:"script_type" enum:"STANDARD,ADMIN"`
	CreatedAt   time.Time         `json:"created_at"`
	UpdatedAt   time.Time         `json:"updated_at"`
	UserID      string            `json:"user_id"`
}

func GetUsers(queries *db.Queries) ([]User, error) {
	usersDB, err := queries.GetUsers(context.Background())
	if err != nil {
		return nil, err
	}

	var users []User

	for _, userDB := range usersDB {
		userIDString, err := converters.PgTypeUUIDToString(userDB.ID)
		if err != nil {
			return nil, err
		}

		_, _, _, _, err = ssh.ParseAuthorizedKey([]byte(userDB.SshKey.String))
		isValidSshKey := true
		if err != nil {
			isValidSshKey = false
		}

		user := User{
			ID:                  *userIDString,
			FullName:            userDB.FullName.String,
			Username:            userDB.Username,
			CustomUsername:      userDB.CustomUsername.String,
			AppRole:             userDB.AppRole.String,
			SSHKey:              userDB.SshKey.String,
			SSHKeyLabel:         userDB.SshKeyLabel.String,
			SSHKeyCreationDate:  userDB.SshKeyCreationDate.Time,
			ValidCustomUsername: len(userDB.CustomUsername.String) > 0 && userDB.CustomUsername.Valid,
			ValidSshKey:         isValidSshKey,
		}
		users = append(users, user)
	}
	return users, nil
}

func GetUserForUserSettings(queries *db.Queries, userID string) (*User, error) {
	userIDPgType, err := converters.StringToPgTypeUUID(userID)
	if err != nil {
		return nil, err
	}

	userDB, err := queries.GetUserForUserSettings(context.Background(), *userIDPgType)
	if err != nil {
		return nil, err
	}

	user := User{
		FullName:           userDB.FullName.String,
		Username:           userDB.Username,
		CustomUsername:     userDB.CustomUsername.String,
		SSHKey:             userDB.SshKey.String,
		SSHKeyLabel:        userDB.SshKeyLabel.String,
		SSHKeyCreationDate: userDB.SshKeyCreationDate.Time,
	}

	return &user, nil
}

func UpdateUserUsername(queries *db.Queries, custom_username pgtype.Text, userID string) error {
	userIDPgType, err := converters.StringToPgTypeUUID(userID)
	if err != nil {
		return err
	}

	existingUser, err := queries.GetUsersWithUsername(context.Background(), custom_username)
	if err != nil && err != sql.ErrNoRows {
		return err
	}

	if existingUser > 0 {
		return fmt.Errorf("username '%s' is already taken", custom_username.String)
	}

	err = queries.UpdateUserUsername(context.Background(), db.UpdateUserUsernameParams{
		CustomUsername: custom_username,
		ID:             *userIDPgType,
	})

	if err != nil {
		return err
	}

	return nil
}

func GetScripts(queries *db.Queries, userID string) ([]Script, error) {
	userIDPgType, err := converters.StringToPgTypeUUID(userID)
	if err != nil {
		return nil, err
	}

	scriptsDB, err := queries.GetScriptsByUser(context.Background(), *userIDPgType)
	if err != nil {
		return nil, err
	}

	var scripts []Script
	for _, scriptDB := range scriptsDB {
		scriptIDString, _ := converters.PgTypeUUIDToString(scriptDB.ID)
		userIDString, _ := converters.PgTypeUUIDToString(scriptDB.UserID)
		script := Script{
			ID:          *scriptIDString,
			Name:        scriptDB.Name,
			Description: scriptDB.Description,
			Content:     scriptDB.Content,
			ScriptType:  scriptDB.ScriptType,
			CreatedAt:   scriptDB.CreatedAt.Time,
			UpdatedAt:   scriptDB.UpdatedAt.Time,
			UserID:      *userIDString,
		}
		scripts = append(scripts, script)
	}
	return scripts, nil
}

func CreateNewScript(queries *db.Queries, name string, description string, content string, scriptType db.ScriptTypeEnum, userID string) (*Script, error) {
	userIDPgType, err := converters.StringToPgTypeUUID(userID)
	if err != nil {
		return nil, err
	}

	newScript, err := queries.CreateScript(context.Background(), db.CreateScriptParams{
		Name:        name,
		Description: description,
		Content:     content,
		UserID:      *userIDPgType,
		CreatedAt: pgtype.Timestamp{
			Time:             time.Now(),
			InfinityModifier: 0,
			Valid:            true,
		},
	})
	if err != nil {
		return nil, err
	}

	scriptIDString, err := converters.PgTypeUUIDToString(newScript.ID)
	if err != nil {
		return nil, err
	}

	userIDString, _ := converters.PgTypeUUIDToString(newScript.UserID)
	if err != nil {
		return nil, err
	}

	scriptResponse := Script{
		ID:          *scriptIDString,
		Name:        newScript.Name,
		Description: newScript.Description,
		Content:     newScript.Content,
		ScriptType:  scriptType,
		CreatedAt:   newScript.CreatedAt.Time,
		UpdatedAt:   newScript.UpdatedAt.Time,
		UserID:      *userIDString,
	}
	return &scriptResponse, nil
}

func UpdateScript(queries *db.Queries, scriptID string, name string, description string, content string, scriptType db.ScriptTypeEnum, userID string) (*Script, error) {
	userIDPgType, err := converters.StringToPgTypeUUID(userID)
	if err != nil {
		return nil, err
	}

	scriptIDPgType, err := converters.StringToPgTypeUUID(scriptID)
	if err != nil {
		return nil, err
	}

	updatedScript, err := queries.EditScript(context.Background(), db.EditScriptParams{
		ID:          *scriptIDPgType,
		Name:        name,
		Description: description,
		Content:     content,
		UserID:      *userIDPgType,
	})
	if err != nil {
		return nil, err
	}

	scriptResponse := Script{
		ID:          scriptID,
		Name:        updatedScript.Name,
		Description: updatedScript.Description,
		Content:     updatedScript.Content,
		ScriptType:  scriptType,
		CreatedAt:   updatedScript.CreatedAt.Time,
		UpdatedAt:   updatedScript.UpdatedAt.Time,
		UserID:      userID,
	}

	return &scriptResponse, nil
}

func DeleteScript(queries *db.Queries, scriptID string, userID string) error {
	scriptIDPgType, err := converters.StringToPgTypeUUID(scriptID)
	if err != nil {
		return err
	}

	userIDPgType, err := converters.StringToPgTypeUUID(userID)
	if err != nil {
		return err
	}

	err = queries.DeleteScript(context.Background(), db.DeleteScriptParams{
		ID:     *scriptIDPgType,
		UserID: *userIDPgType,
	})
	if err != nil {
		return err
	}

	return nil
}
