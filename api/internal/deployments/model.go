package deployments

import (
	"context"
	"fmt"
	"slices"
	"strings"
	"time"

	"github.com/jackc/pgx/v5/pgtype"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/converters"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/db"
)

type DeploymentModel struct {
	ID              string                  `json:"id" format:"uuid"`
	TerraformModule string                  `json:"terraform_module"`
	TerraformLogs   string                  `json:"terraform_logs"`
	Status          db.DeploymentStatusEnum `json:"status"`
	CreatedAt       time.Time               `json:"created_at"`
	NodeID          string                  `json:"node_id" format:"uuid"`
	NodeName        string                  `json:"node_name"`
	EngagementID    string                  `json:"engagement_id" format:"uuid"`
	Title           string                  `json:"title"`
	UserID          string                  `json:"user_id" format:"uuid"`
	UserName        string                  `json:"username"`
	ErrorMessage    string                  `json:"error_message,omitempty"`
	Warnings        []string                `json:"warnings,omitempty"`
}

// GetDeployments retrieves deployments, filtering by engagement IDs based on the user's role
func GetDeployments(queries *db.Queries, roles []string, userID string) ([]DeploymentModel, error) {
	userIDPgType, err := converters.StringToPgTypeUUID(userID)

	if err != nil {
		return nil, fmt.Errorf("failed to convert userID to pgtype.UUID: %w", err)
	}

	isAdmin := slices.Contains(roles, "Admin")

	var engagementIDs []pgtype.UUID
	if isAdmin {
		allEngagements, err := queries.GetAllEngagements(context.Background())
		if err != nil {
			return nil, fmt.Errorf("failed to fetch all engagements: %w", err)
		}
		for _, engagement := range allEngagements {
			engagementIDs = append(engagementIDs, engagement.ID)
		}
	} else {
		userEngagements, err := queries.GetUserEngagements(context.Background(), *userIDPgType)
		if err != nil {
			return nil, fmt.Errorf("failed to fetch user engagements: %w", err)
		}
		for _, engagement := range userEngagements {
			engagementIDs = append(engagementIDs, engagement.ID)
		}
	}

	deploymentsDB, err := queries.GetDeployments(context.Background(), engagementIDs)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch deployments by engagement IDs: %w", err)
	}

	var deployments []DeploymentModel
	for _, dbDeployment := range deploymentsDB {
		deploymentIDString, _ := converters.PgTypeUUIDToString(dbDeployment.ID)
		deploymentNodeIDString, _ := converters.PgTypeUUIDToString(dbDeployment.NodeID)
		engagementNodeIDString, _ := converters.PgTypeUUIDToString(dbDeployment.EngagementID)
		userIDString, _ := converters.PgTypeUUIDToString(dbDeployment.UserID)

		deployment := DeploymentModel{
			ID:              *deploymentIDString,
			TerraformModule: dbDeployment.TerraformModule,
			TerraformLogs:   dbDeployment.TerraformLogs,
			Status:          dbDeployment.Status,
			CreatedAt:       dbDeployment.CreatedAt.Time,
			NodeID:          *deploymentNodeIDString,
			NodeName:        dbDeployment.NodeName,
			EngagementID:    *engagementNodeIDString,
			Title:           dbDeployment.Title,
			UserID:          *userIDString,
			UserName:        dbDeployment.Username,
			ErrorMessage:    dbDeployment.ErrorMessage.String,
		}
		deployments = append(deployments, deployment)
	}

	return deployments, nil
}

// Get Deployment Details

func GetDeploymentDetails(queries *db.Queries, deploymentID string, userID string) (*DeploymentModel, error) {
	deploymentIDPgType, err := converters.StringToPgTypeUUID(deploymentID)
	if err != nil {
		return nil, err
	}

	deploymentDB, err := queries.GetDeployment(context.Background(), *deploymentIDPgType)
	if err != nil {
		return nil, err
	}

	deploymentIDString, _ := converters.PgTypeUUIDToString(deploymentDB.EngagementID)
	deploymentNodeIDString, _ := converters.PgTypeUUIDToString(deploymentDB.NodeID)

	deployment := DeploymentModel{
		ID:              deploymentID,
		EngagementID:    *deploymentIDString,
		TerraformModule: deploymentDB.TerraformModule,
		TerraformLogs:   deploymentDB.TerraformLogs,
		Status:          deploymentDB.Status,
		CreatedAt:       deploymentDB.CreatedAt.Time,
		Title:           deploymentDB.Title,
		UserName:        deploymentDB.Username,
		NodeID:          *deploymentNodeIDString,
		UserID:          userID,
		NodeName:        deploymentDB.NodeName,
	}

	if deploymentDB.Status == db.DeploymentStatusEnumERROR {
		deployment.ErrorMessage = deploymentDB.ErrorMessage.String
	}
	if deploymentDB.Status == db.DeploymentStatusEnumWARNING {
		deployment.Warnings = strings.Split(deploymentDB.ErrorMessage.String, "\n")
	}

	return &deployment, nil
}
