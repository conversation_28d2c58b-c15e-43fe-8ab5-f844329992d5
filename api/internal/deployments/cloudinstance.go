package deployments

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"slices"
	"text/template"
	"time"

	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/deployments/keys"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5/pgtype"
	amqp "github.com/rabbitmq/amqp091-go"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/converters"

	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/db"
)

type BaseCloudDeployment struct {
	RabbitMQChannel     *amqp.Channel
	DBQueries           *db.Queries
	UserID              pgtype.UUID
	NodeID              pgtype.UUID
	EngagementID        pgtype.UUID
	Region              string
	Name                string
	StartupScript       string
	SSHPublicKey        string
	InstanceType        string
	OpenIngressTcpPorts []int32
	AwsRootRegion       string
	AccountID           string
}

type AwsCloudInstanceDeployment struct {
	BaseCloudDeployment
	OperatingSystemImageID string
	AccessKey              string
	SecretKey              string
}

type AzureCloudInstanceDeployment struct {
	BaseCloudDeployment
	ClientID       string
	ClientSecret   string
	TenantID       string
	ImagePublisher string
	ImageOffer     string
	ImageSKU       string
	ImageVersion   string
	SubscriptionID string
}

// Helpers to be shared accross the clouds
func FetchSecrets(sm *keys.SecretsManager, accountID string, requiredKeys []string) (map[string]string, error) {
	secrets, err := sm.GetSecret(accountID)
	if err != nil {
		return nil, err
	}

	for _, key := range requiredKeys {
		if _, ok := secrets[key]; !ok {
			return nil, fmt.Errorf("missing required secret key: %s", key)
		}
	}
	return secrets, nil
}

func (b *BaseCloudDeployment) WriteDeployment(
	templateName string,
	templateData any,
	rabbitQueue string,
	provider string,
	deploymentID string,
) error {
	tplContent, err := b.DBQueries.GetTerraformTemplateContentByName(context.TODO(), templateName)
	if err != nil {
		return fmt.Errorf("template fetch error: %w", err)
	}
	tmpl, err := template.New("tpl").Parse(tplContent)
	if err != nil {
		return fmt.Errorf("template parse error: %w", err)
	}

	var buf bytes.Buffer
	if err := tmpl.Execute(&buf, templateData); err != nil {
		return fmt.Errorf("template execution error: %w", err)
	}

	deploymentIDPg, _ := converters.StringToPgTypeUUID(deploymentID)
	now := time.Now()

	err = b.DBQueries.CreateDeployment(context.TODO(), db.CreateDeploymentParams{
		ID:              *deploymentIDPg,
		TerraformModule: buf.String(),
		TerraformLogs:  "", // Initialize with empty logs
		Status:          db.DeploymentStatusEnumPENDING,
		UserID:          b.UserID,
		NodeID:          b.NodeID,
		EngagementID:    b.EngagementID,
		CreatedAt: pgtype.Timestamp{
			Time:             now,
			InfinityModifier: 0,
			Valid:            true,
		},
	})
	if err != nil {
		return fmt.Errorf("db store error: %w", err)
	}

	userIDStr, _ := converters.PgTypeUUIDToString(b.UserID)
	nodeIDStr, _ := converters.PgTypeUUIDToString(b.NodeID)
	engagementIDStr, _ := converters.PgTypeUUIDToString(b.EngagementID)

	payload := Deployment{
		ID:              deploymentID,
		UserID:          *userIDStr,
		NodeID:          *nodeIDStr,
		EngagementID:    *engagementIDStr,
		Provider:        provider,
		TerraformModule: buf.String(),
		AccountID:       b.AccountID,
		CreatedAt:       now,
	}

	body, err := json.Marshal(payload)
	if err != nil {
		return fmt.Errorf("marshal error: %w", err)
	}

	return b.RabbitMQChannel.PublishWithContext(context.TODO(), "", rabbitQueue, false, false, amqp.Publishing{
		ContentType: "application/json",
		Body:        body,
	})
}

func NewAwsCloudInstanceDeployment(
	dbQueries *db.Queries,
	rabbitmqChannel *amqp.Channel,
	userID pgtype.UUID,
	nodeID pgtype.UUID,
	engagementID pgtype.UUID,
	region string,
	operatingSystemImageID string,
	instanceType string,
	name string,
	openIngressTcpPorts []int32,
	startupScript string,
	sshPublicKey string,
	awsRootRegion string,
	accountID string,
) (*AwsCloudInstanceDeployment, error) {

	sm, err := keys.NewSecretsManager(awsRootRegion)
	if err != nil {
		return nil, err
	}
	requiredKeys := []string{"access_key_id", "access_key_secret"}
	secretValues, err := FetchSecrets(sm, accountID, requiredKeys)
	if err != nil {
		return nil, err
	}

	return &AwsCloudInstanceDeployment{
		BaseCloudDeployment: BaseCloudDeployment{
			RabbitMQChannel:     rabbitmqChannel,
			DBQueries:           dbQueries,
			UserID:              userID,
			NodeID:              nodeID,
			EngagementID:        engagementID,
			Region:              region,
			Name:                name,
			StartupScript:       startupScript,
			SSHPublicKey:        sshPublicKey,
			InstanceType:        instanceType,
			OpenIngressTcpPorts: openIngressTcpPorts,
			AwsRootRegion:       awsRootRegion,
			AccountID:           accountID,
		},
		OperatingSystemImageID: operatingSystemImageID,
		AccessKey:              secretValues["access_key_id"],
		SecretKey:              secretValues["access_key_secret"],
	}, nil
}

func (c *AwsCloudInstanceDeployment) WriteTemplate() error {
	// Ensure port 22 is open
	if !slices.Contains(c.OpenIngressTcpPorts, 22) {
		c.OpenIngressTcpPorts = append(c.OpenIngressTcpPorts, 22)
	}
	deploymentID := uuid.New().String()

	templateData := struct {
		DeploymentID           string
		Region                 string
		AccessKey              string
		SecretKey              string
		OperatingSystemImageID string
		InstanceType           string
		Name                   string
		OpenIngressTcpPorts    []int32
		StartupScript          string
		SshPublicKey           string
	}{
		DeploymentID:           deploymentID,
		Region:                 c.Region,
		AccessKey:              c.AccessKey,
		SecretKey:              c.SecretKey,
		OperatingSystemImageID: c.OperatingSystemImageID,
		InstanceType:           c.InstanceType,
		Name:                   c.Name,
		OpenIngressTcpPorts:    c.OpenIngressTcpPorts,
		StartupScript:          c.StartupScript,
		SshPublicKey:           c.SSHPublicKey,
	}

	return c.BaseCloudDeployment.WriteDeployment(
		"aws_ec2_instance", // template name
		templateData,
		c.EngagementID.String(), // queue name
		"AWS",
		deploymentID,
	)
}

// ========= AZURE =============

func NewAzureCloudInstanceDeployment(
	dbQueries *db.Queries,
	rabbitmqChannel *amqp.Channel,
	userID pgtype.UUID,
	nodeID pgtype.UUID,
	engagementID pgtype.UUID,
	region string,
	imagePublisher string,
	imageOffer string,
	imageSKU string,
	imageVersion string,
	vmSize string,
	name string,
	openIngressTcpPorts []int32,
	startupScript string,
	awsRootRegion string,
	sshPublicKey string,
	accountID string,

) (*AzureCloudInstanceDeployment, error) {
	sm, err := keys.NewSecretsManager(awsRootRegion)
	if err != nil {
		return nil, err
	}
	requiredKeys := []string{"AppID", "AppSecret", "TenantID"}
	secretValues, err := FetchSecrets(sm, accountID, requiredKeys)
	if err != nil {
		return nil, err
	}

	accountUUID, err := converters.StringToPgTypeUUID(accountID)
	if err != nil {
		return nil, err
	}

	subscriptionID, err := dbQueries.GetAzureSubscriptionIDById(context.Background(), *accountUUID)

	if err != nil {
		return nil, fmt.Errorf("failed to get subscription id")
	}
	return &AzureCloudInstanceDeployment{
		BaseCloudDeployment: BaseCloudDeployment{
			RabbitMQChannel:     rabbitmqChannel,
			DBQueries:           dbQueries,
			UserID:              userID,
			NodeID:              nodeID,
			EngagementID:        engagementID,
			Region:              region,
			Name:                name,
			StartupScript:       startupScript,
			SSHPublicKey:        sshPublicKey,
			AccountID:           accountID,
			InstanceType:        vmSize,
			OpenIngressTcpPorts: openIngressTcpPorts,
		},
		ClientID:       secretValues["AppID"],
		ClientSecret:   secretValues["AppSecret"],
		TenantID:       secretValues["TenantID"],
		ImagePublisher: imagePublisher,
		ImageOffer:     imageOffer,
		ImageSKU:       imageSKU,
		ImageVersion:   imageVersion,
		SubscriptionID: subscriptionID.String,
	}, nil
}

func (c *AzureCloudInstanceDeployment) WriteTemplate() error {
	if !slices.Contains(c.OpenIngressTcpPorts, 22) {
		c.OpenIngressTcpPorts = append(c.OpenIngressTcpPorts, 22)
	}

	// Define a struct for ingress rules with priority as Azure requires priority
	type IngressRule struct {
		Port     int32
		Priority int
	}

	var ingressRules []IngressRule
	for i, port := range c.OpenIngressTcpPorts {
		ingressRules = append(ingressRules, IngressRule{
			Port:     port,
			Priority: 1000 + i,
		})
	}

	// default startup script as Azure requires a value for custom script if included in tf. If user chooses a real startup script, then adds that,
	// if user does not select the script, then this will be added as default to pass the Azure terraform validation
	var script string
	if c.StartupScript != "" {
		script = c.StartupScript
	} else {
		script = "#!/bin/bash echo 'No-op startup script for Azure.'"
	}

	deploymentID := uuid.New().String()

	// Template input
	templateData := struct {
		DeploymentID   string
		SubscriptionID string
		ClientID       string
		ClientSecret   string
		TenantID       string
		Region         string
		ImagePublisher string
		ImageOffer     string
		ImageSKU       string
		ImageVersion   string
		InstanceType   string
		Name           string
		StartupScript  string
		SshPublicKey   string
		IngressRules   []IngressRule
	}{
		DeploymentID:   deploymentID,
		SubscriptionID: c.SubscriptionID,
		ClientID:       c.ClientID,
		ClientSecret:   c.ClientSecret,
		TenantID:       c.TenantID,
		Region:         c.Region,
		ImagePublisher: c.ImagePublisher,
		ImageOffer:     c.ImageOffer,
		ImageSKU:       c.ImageSKU,
		ImageVersion:   c.ImageVersion,
		InstanceType:   c.InstanceType,
		Name:           c.Name,
		StartupScript:  script,
		SshPublicKey:   c.SSHPublicKey,
		IngressRules:   ingressRules,
	}

	return c.BaseCloudDeployment.WriteDeployment(
		"azure_vm_instance", // template name
		templateData,
		c.EngagementID.String(), // queue name
		"AZURE",
		deploymentID)
}
