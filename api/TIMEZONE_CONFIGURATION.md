# Timezone Configuration Guide

This guide explains how to configure user-friendly timestamps in the Engage API.

## Current Implementation

The API now supports flexible timezone handling for log timestamps. Instead of always using UTC or hardcoded CET, you can configure the timezone based on your users' needs.

## Configuration Options

### 1. Environment Variable (Recommended for now)

Set the `DEFAULT_USER_TIMEZONE` environment variable to specify the default timezone for all users:

```bash
# For European users (CET/CEST)
export DEFAULT_USER_TIMEZONE="Europe/Berlin"

# For US East Coast users (EST/EDT)
export DEFAULT_USER_TIMEZONE="America/New_York"

# For US West Coast users (PST/PDT)
export DEFAULT_USER_TIMEZONE="America/Los_Angeles"

# For US Central users (CST/CDT)
export DEFAULT_USER_TIMEZONE="America/Chicago"

# For UK users (GMT/BST)
export DEFAULT_USER_TIMEZONE="Europe/London"

# For UTC (default fallback)
export DEFAULT_USER_TIMEZONE="UTC"
```

### 2. Available Convenience Functions

You can also use predefined convenience functions in your code:

```go
import "gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/utils"

// Specific timezone functions
timestamp := utils.GetCETTimestamp()    // Europe/Berlin
timestamp := utils.GetESTTimestamp()    // America/New_York  
timestamp := utils.GetPSTTimestamp()    // America/Los_Angeles
timestamp := utils.GetCSTTimestamp()    // America/Chicago
timestamp := utils.GetUTCTimestamp()    // UTC

// Flexible timezone function
timestamp := utils.GetTimestampInTimezone("Asia/Tokyo")

// User-specific function (uses environment variable or defaults to UTC)
timestamp := utils.GetUserTimestamp(userID)
```

## Common Timezone Values

| Region | Timezone String | Description |
|--------|----------------|-------------|
| Europe (CET) | `Europe/Berlin` | Central European Time |
| Europe (UK) | `Europe/London` | Greenwich Mean Time |
| US East Coast | `America/New_York` | Eastern Time |
| US West Coast | `America/Los_Angeles` | Pacific Time |
| US Central | `America/Chicago` | Central Time |
| US Mountain | `America/Denver` | Mountain Time |
| Japan | `Asia/Tokyo` | Japan Standard Time |
| Australia | `Australia/Sydney` | Australian Eastern Time |
| UTC | `UTC` | Coordinated Universal Time |

## Future Enhancements

### Database-Based User Preferences

In the future, you can extend the user table to include timezone preferences:

```sql
-- Add timezone column to users table
ALTER TABLE users ADD COLUMN timezone TEXT DEFAULT 'UTC';
```

Then update the `GetUserTimestamp` function to read from the database:

```go
func GetUserTimestamp(userID string) pgtype.Timestamp {
    // Check user's timezone preference from database
    userTimezone := getUserTimezoneFromDB(userID)
    if userTimezone != "" {
        return GetTimestampInTimezone(userTimezone)
    }
    
    // Fallback to environment variable or UTC
    if defaultTZ := os.Getenv("DEFAULT_USER_TIMEZONE"); defaultTZ != "" {
        return GetTimestampInTimezone(defaultTZ)
    }
    
    return GetUTCTimestamp()
}
```

## Testing

To test different timezones, you can temporarily set the environment variable:

```bash
# Test with CET
DEFAULT_USER_TIMEZONE="Europe/Berlin" go run ./cmd/api

# Test with EST
DEFAULT_USER_TIMEZONE="America/New_York" go run ./cmd/api

# Test with UTC (default)
unset DEFAULT_USER_TIMEZONE
go run ./cmd/api
```

## Current Usage

The following operations now use user-friendly timestamps:

- Cloud instance state changes
- Deployment IP assignments  
- AWS instance synchronization
- Activity log entries

All these will respect the `DEFAULT_USER_TIMEZONE` environment variable or fall back to UTC.
