/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
    // "./node_modules/primereact/**/*.{js,ts,jsx,tsx}",
  ],
  darkMode: "class",
  theme: {
    sm: "640px",
    // => @media (min-width: 640px)
    md: "1024px",
    // => @media (min-width: 1024px)
    lg: "1280px",
    // => @media (min-width: 1280px)
    extend: {
      colors: {
        "svg-purple": "#7500C0",
        white: "#FFF",
        blue: "#0EA5E9",
        darkbg: "#1f2528",
      },
    },
  },
  variants: {
    extend: {
      fill: ["hover", "focus"],
    },
  },
  plugins: [],
};
