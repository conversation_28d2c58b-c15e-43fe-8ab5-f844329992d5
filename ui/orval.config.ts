import dotenv from "dotenv";
import { defineConfig } from "orval";

dotenv.config();

export default defineConfig({
  engage: {
    output: {
      // httpClient: "fetch",
      // client: "fetch",
      client: "react-query",
      prettier: true,
      target: "./src/client.ts",
      schemas: "./src/model",
      baseUrl: "",
      override: {
        mutator: {
          path: "./src/services/axiosInstance.ts",
          name: "customInstance",
        },
      },
    },
    input: {
      target: "./document.yaml",
    },
    hooks: {
      afterAllFilesWrite: "prettier --write",
    },
  },
});
