/**
 * Generated by orval v7.11.2 🍺
 * Do not edit manually.
 * Engage API
 * OpenAPI spec version: 2.0.0
 */
import type { EditEngagementInputBodyUserIds } from "./editEngagementInputBodyUserIds";

export interface EditEngagementInputBody {
  /** A URL to the JSON Schema for this object. */
  readonly $schema?: string;
  title: string;
  /** User IDs */
  user_ids: EditEngagementInputBodyUserIds;
  wbs_code: string;
}
