/**
 * Generated by orval v7.11.2 🍺
 * Do not edit manually.
 * Engage API
 * OpenAPI spec version: 2.0.0
 */

export interface CreateNodeTypePersonInputBody {
  /** A URL to the JSON Schema for this object. */
  readonly $schema?: string;
  /** @maxLength 100 */
  company?: string;
  email?: string;
  /** Engagement ID */
  engagement_id: string;
  /** @maxLength 100 */
  first_name: string;
  /** @maxLength 100 */
  last_name?: string;
  /** Node Group ID of the Engagement that the Node will be created in. If this attribute is not provided, a new Node Group will be created */
  node_group_id?: string;
  /** @maxLength 100 */
  title?: string;
}
