/**
 * Generated by orval v7.11.2 🍺
 * Do not edit manually.
 * Engage API
 * OpenAPI spec version: 2.0.0
 */
import type { GetNodeCloudInstanceOutputBodyActivityLogs } from "./getNodeCloudInstanceOutputBodyActivityLogs";
import type { NodeCloudInstance } from "./nodeCloudInstance";

export interface GetNodeCloudInstanceOutputBody {
  /** A URL to the JSON Schema for this object. */
  readonly $schema?: string;
  /** Activity Logs */
  activity_logs?: GetNodeCloudInstanceOutputBodyActivityLogs;
  node: NodeCloudInstance;
}
