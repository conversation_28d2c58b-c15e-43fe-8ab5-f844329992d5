/**
 * Generated by orval v7.11.2 🍺
 * Do not edit manually.
 * Engage API
 * OpenAPI spec version: 2.0.0
 */
import type { EditNodeTypeCloudInstanceOutputBodyOpenIngressTcpPorts } from "./editNodeTypeCloudInstanceOutputBodyOpenIngressTcpPorts";
import type { EditNodeTypeCloudInstanceOutputBodyProvider } from "./editNodeTypeCloudInstanceOutputBodyProvider";
import type { EditNodeTypeCloudInstanceOutputBodyPublicIpv4Address } from "./editNodeTypeCloudInstanceOutputBodyPublicIpv4Address";

export interface EditNodeTypeCloudInstanceOutputBody {
  /** A URL to the JSON Schema for this object. */
  readonly $schema?: string;
  /** AWS Instance type */
  instance_type: string;
  /**
   * Instance name
   * @minLength 1
   * @maxLength 256
   * @pattern ^[a-zA-Z0-9 _.:/=+\-@]{1,256}$
   */
  name: string;
  node_id: string;
  /** Open ingress TCP ports */
  open_ingress_tcp_ports: EditNodeTypeCloudInstanceOutputBodyOpenIngressTcpPorts;
  operating_system_image_id: string;
  provider: EditNodeTypeCloudInstanceOutputBodyProvider;
  public_ipv4_address: EditNodeTypeCloudInstanceOutputBodyPublicIpv4Address;
  region: string;
}
