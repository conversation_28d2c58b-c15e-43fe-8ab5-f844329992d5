/**
 * Generated by orval v7.11.2 🍺
 * Do not edit manually.
 * Engage API
 * OpenAPI spec version: 2.0.0
 */

export interface InventoryDomainResponse {
  activity_count: number;
  age?: number;
  client?: string;
  created_at?: string;
  engagement?: string;
  id?: string;
  last_activity?: string;
  message?: string;
  purchase_date?: string;
  registrar?: string;
  renewal_date?: string;
  status?: string;
  type?: string;
  url: string;
  username?: string;
}
