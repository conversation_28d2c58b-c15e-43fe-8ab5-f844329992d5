import { createColumnHelper } from "@tanstack/react-table";
import { format } from "date-fns/format";
import { IoIosInformationCircleOutline } from "react-icons/io";
import { InventoryCloudInstance } from "../model";


type TableRow = InventoryCloudInstance;

const columnHelper = createColumnHelper<TableRow>();

// Helper function to determine if instance is live (current)
const isInstanceLive = (instance: InventoryCloudInstance): boolean => {
  // Check if IPs match and are not empty
  const ipsMatch = instance.event_ip === instance.public_ipv4_address &&
                   instance.event_ip !== '' &&
                   instance.public_ipv4_address !== '';

  // Check if instance state is running (only running instances can be live)
  const isRunning = instance.cloud_instance_state?.toLowerCase() === 'running';

  return ipsMatch && isRunning;
};

export const cloud_instance_columns = (
  openEditModal: (node: TableRow) => void,
) => [
  columnHelper.accessor("name", {
    header: () => "Name",
    cell: (info) => {
      const isLive = isInstanceLive(info.row.original);
      return (
        <div className="flex items-center space-x-3 py-4">
          {/* Status indicator dot */}

          {/* Instance name */}
          <span className={`max-w-[140px] min-w-[100px] truncate font-medium ${
            isLive ? 'text-green-700' : 'text-gray-700'
          }`}>
            {info.getValue()}
          </span>        
        </div>
      );
    },
    meta: {
      getRowClassName: (row: { original: InventoryCloudInstance }) => {
        const isLive = isInstanceLive(row.original);
        if (isLive) {
          return 'bg-green-50 border-l-4 border-green-400 hover:bg-green-100 transition-colors';
        }
        return 'bg-gray-50 border-l-4 border-gray-300 hover:bg-gray-100 transition-colors';
      },
    },
  }),
  columnHelper.accessor("cloud_instance_state", {
    header: () => "State",
    cell: (info) => (
      <div className="max-w-[120px] min-w-[80px] truncate py-4">
        {info.getValue()?.toUpperCase()}
      </div>
    ),
  }),
  columnHelper.accessor("cloud_instance_id", {
    header: () => "Instance ID",
    cell: (info) => (
      <div className="max-w-[150px] min-w-[100px] truncate py-4">
        {info.getValue()}
      </div>
    ),
  }),
  columnHelper.accessor("ci_deployment_status", {
    header: () => "Deploy Status",
    cell: (info) => (
      <div className="max-w-[120px] min-w-[90px] truncate py-4">
        {info.getValue()}
      </div>
    ),
  }),
  columnHelper.accessor("operating_system_image_id", {
    header: () => "OS Image ID",
    cell: (info) => (
      <div className="max-w-[140px] min-w-[100px] truncate py-4">
        {info.getValue() as string}
      </div>
    ),
  }),
  columnHelper.accessor("provider", {
    header: () => "Provider",
    cell: (info) => (
      <div className="max-w-[100px] min-w-[70px] truncate py-4">
        {info.getValue() as string}
      </div>
    ),
  }),

  columnHelper.accessor("region", {
    header: () => "Region",
    cell: (info) => (
      <div className="max-w-[100px] min-w-[70px] truncate py-4">
        {info.getValue() as string}
      </div>
    ),
  }),
  columnHelper.accessor("event_ip", {
    header: () => "Legacy IP",
    cell: (info) => {
      const isLive = isInstanceLive(info.row.original);
      const eventIp = info.getValue() as string;
      const currentIp = info.row.original.public_ipv4_address;

      // Hide event IP if it's the same as current IP
      const shouldShowEventIp = eventIp && eventIp !== currentIp;

      return (
        <div className="flex items-center space-x-2 py-4">
          <span className={`font-mono max-w-[120px] min-w-[100px] truncate ${
            isLive ? 'text-green-700 font-semibold' : 'text-gray-600'
          }`}>
            {shouldShowEventIp ? eventIp : '-'}
          </span>
          {/* {isLive && eventIp } */}
        </div>
      );
    },
  }),
  columnHelper.accessor("public_ipv4_address", {
    header: () => "Current IP",
    cell: (info) => {
      const isLive = isInstanceLive(info.row.original);
      const currentIp = info.getValue() as string;

      return (
        <div className="flex items-center space-x-2 py-4">
          <span className={`font-mono max-w-[120px] min-w-[100px] truncate ${
            isLive ? 'text-green-700 font-semibold' : 'text-gray-600'
          }`}>
            {currentIp || '-'}
          </span>
        </div>
      );
    },
  }),
  columnHelper.accessor("open_ports", {
    header: () => "Ports",
    cell: (info) => {
      const openPorts = info.getValue() as number[];
      return openPorts ? (
        <div className="max-w-[120px] min-w-[80px] truncate py-4">
          {openPorts.join(", ")}
        </div>
      ) : (
        <div className="py-4">-</div>
      );
    },
  }),
  columnHelper.accessor("node_created_at", {
    header: () => "Created",
    cell: (info) => (
      <div className="max-w-[130px] min-w-[100px] truncate py-4">
        {format(new Date(info.getValue() as string), "MMM d, yy")}
      </div>
    ),
    filterFn: "dateRange",
    meta: {
      filterVariant: "date",
    },
  }),
  columnHelper.accessor("client_name", {
    header: () => "Client",
    cell: (info) => (
      <div className="max-w-[120px] min-w-[80px] truncate py-4">
        {info.getValue() as string}
      </div>
    ),
  }),
  {
    id: "infoColumn",
    header: () => "",
    cell: ({ row }: { row: { original: TableRow } }) => (
      <div className="py-4">
        <IoIosInformationCircleOutline
          className="h-5 w-5"
          onClick={() => openEditModal(row.original)}
        />
      </div>
    ),
  },
];
