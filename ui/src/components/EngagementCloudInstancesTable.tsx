import { createColumnHelper } from "@tanstack/react-table";
import { format } from "date-fns";
import { HTMLProps, useEffect, useRef, useState, forwardRef, useImperativeHandle } from "react";
import { IoIosInformationCircleOutline } from "react-icons/io";
import { FaAws, FaMicrosoft } from "react-icons/fa";

import { CloudInstance } from "../model";
import { CloudInstanceNodeGroups } from "../model";
import { getIconType } from "../utils/assets.tsx";
import EditNodeModal from "./GraphView/EditNodeModal";
import Table from "./Table";

type TableRow = CloudInstance;

const columnHelper = createColumnHelper<TableRow>();


// Map cloud instance state to a Tailwind background color class
const stateColorClass = (state?: string | null) => {
  const s = (state || "").toLowerCase();
  switch (s) {
    case "terminated":
      return "bg-gray-500"; // per request
    case "running":
      return "bg-green-500";
    case "pending":
      return "bg-yellow-500";
    case "stopping":
      return "bg-orange-500";
    case "stopped":
      return "bg-slate-500";
    case "shutting-down":
      return "bg-gray-400";
    case "error":
      return "bg-red-500";
    default:
      return "bg-slate-400";
  }
};

const columns = (
  openEditModal: (node: TableRow) => void,
) => [
  columnHelper.accessor("name", {
    header: () => "Name",
    enableResizing: false,
    cell: ({ row, getValue }) => (
      <div className="flex flex-row items-center space-x-2 p-2">
        <div className={`h-3 w-3 rounded-sm ${stateColorClass(row.original.cloud_instance_state)}`}></div>
        <IndeterminateCheckbox
          {...{
            checked: row.getIsSelected(),
            indeterminate: row.getIsSomeSelected(),
            onChange: row.getToggleSelectedHandler(),
          }}
        />
        {getIconType(row.original.type)}
        {String((row.original.provider || "").toUpperCase()) === "AZURE" ? (
          <FaMicrosoft className="h-4 w-4 opacity-80 text-blue-500" />
        ) : (
          <FaAws className="h-4 w-4 opacity-80 text-orange-500" />
        )}
        <div>{getValue()}</div>
      </div>
    ),
  }),
  columnHelper.accessor("id", {
    header: () => "ID",
    cell: (info) => <div className="pl-4">{info.getValue()}</div>,
  }),
  columnHelper.accessor("cloud_instance_state", {
    header: () => "Cloud Instance State",
    cell: (info) => {
      const s = ((info.getValue() as string) || "").toLowerCase();
      const isTransient = s === "pending" || s === "stopping" || s === "shutting-down";
      return (
        <div className="pl-4 flex items-center gap-2">
          <span>{(info.getValue() as string)?.toUpperCase()}</span>
          {isTransient && (
            <span
              aria-label="loading"
              className="inline-block h-3 w-3 animate-spin rounded-full border-2 border-gray-300 border-t-gray-600"
            />
          )}
        </div>
      );
    },
  }),
  columnHelper.accessor("cloud_instance_id", {
    header: () => "Cloud Instance ID",
    cell: (info) => <div className="pl-4">{info.getValue() as string}</div>,
  }),
  columnHelper.accessor("ci_deployment_status", {
    header: () => "Deployment Status",
    cell: (info) => <div className="pl-4">{info.getValue() as string}</div>,
  }),
  columnHelper.accessor("type", {
    header: () => "Type",
    cell: (info) => <div className="pl-4">{info.getValue() as string}</div>,
  }),
  columnHelper.accessor("operating_system_image_id", {
    header: () => "Operating System Image ID",
    cell: (info) => <div className="pl-4">{info.getValue() as string}</div>,
  }),
  columnHelper.accessor("provider", {
    header: () => "Provider",
    cell: (info) => <div className="pl-4">{info.getValue() as string}</div>,
  }),
  columnHelper.accessor("region", {
    header: () => "Region",
    cell: (info) => <div className="pl-4">{info.getValue() as string}</div>,
  }),
  columnHelper.accessor("public_ipv4_address", {
    header: () => "Public IPv4 Address",
    cell: (info) => <div className="pl-4">{info.getValue() as string}</div>,
  }),
  columnHelper.accessor("open_ports", {
    header: () => "Open Ports",
    cell: (info) => {
      const openPorts = info.getValue() as number[];
      return openPorts ? (
        <div className="pl-4">{openPorts.join(", ")}</div>
      ) : null;
    },
  }),
  columnHelper.accessor("created_at", {
    header: () => "Created",
    cell: (info) => (
      <div className="pl-4">
        {format(new Date(info.getValue() as string), "dd-MMM-yyyy, HH:mm")}
      </div>
    ),
    filterFn: "dateRange",
    meta: {
      filterVariant: "date",
    },
  }),
  columnHelper.accessor("updated_at", {
    header: () => "Last Update",
    cell: (info) => (
      <div className="pl-4">
        {format(new Date(info.getValue() as string), "dd-MMM-yyyy, HH:mm")}
      </div>
    ),
    filterFn: "dateRange",
    meta: {
      filterVariant: "date",
    },
  }),
  {
    id: "infoColumn",
    header: () => "",
    cell: ({ row }: { row: { original: TableRow } }) => (
      <div>
        <IoIosInformationCircleOutline
          className="h-5 w-5"
          onClick={() => openEditModal(row.original)}
        />
      </div>
    ),
  },
];

function IndeterminateCheckbox({
  indeterminate,
  className = "",
  ...rest
}: { indeterminate?: boolean } & HTMLProps<HTMLInputElement>) {
  const ref = useRef<HTMLInputElement>(null!);

  useEffect(() => {
    if (typeof indeterminate === "boolean") {
      ref.current.indeterminate = !rest.checked && indeterminate;
    }
  }, [ref, indeterminate, rest.checked]);

  return (
    <input
      type="checkbox"
      ref={ref}
      className={className + " cursor-pointer"}
      {...rest}
    />
  );
}

export interface EngagementCloudInstancesTableRef {
  clearSelection: () => void;
}

type Props = {
  nodeGroups: CloudInstanceNodeGroups[];
  engagementName: string;
  onSelectionChange?: (nodeIds: string[]) => void;
  onSelectionStatesChange?: (states: string[]) => void;
};

const EngagementCloudInstancesTable = forwardRef<EngagementCloudInstancesTableRef, Props>(
  ({ nodeGroups, engagementName, onSelectionChange, onSelectionStatesChange }, ref) => {
    const [isOpenEditModal, setIsOpenEditModal] = useState<boolean>(false);
    const [selectedRow, setSelectedRow] = useState<TableRow | null>(null);
    const tableRef = useRef<{ clearSelection: () => void } | null>(null);

    const openEditModal = (row: TableRow) => {
      setSelectedRow(row);
      setIsOpenEditModal(true);
    };

    const closeEditModal = () => {
      setSelectedRow(null);
      setIsOpenEditModal(false);
    };

    // Flatten instances from node groups to first-level rows and sort by created_at (newest first)
    const flattenedInstances: CloudInstance[] = (nodeGroups || [])
      .flatMap((ng) => ng.cloud_instances || [])
      .filter((ci): ci is CloudInstance => Boolean(ci))
      .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());

    // Collect selected row ids and states and pass to parent
    const handleSelection = (rows: { original: TableRow }[]) => {
      const selected = rows.map((r) => r.original);
      const ids = selected.map((ci) => ci.id);
      const states = selected.map((ci) => String(ci.cloud_instance_state || ""));
      onSelectionChange?.(ids);
      onSelectionStatesChange?.(states);
    };

    // Expose clear selection method to parent
    useImperativeHandle(ref, () => ({
      clearSelection: () => {
        if (tableRef.current) {
          tableRef.current.clearSelection();
        }
      },
    }));

    return (
      <>
        <Table
          data={flattenedInstances}
          columns={columns(openEditModal)}
          onRowSelectionChange={handleSelection}
          tableRef={tableRef}
        />
        <EditNodeModal
          isOpen={isOpenEditModal}
          closeModal={closeEditModal}
          nodeDetails={selectedRow}
          isNodeGraph={false}
          engagementName={engagementName}
        />
      </>
    );
  }
);

EngagementCloudInstancesTable.displayName = "EngagementCloudInstancesTable";

export default EngagementCloudInstancesTable;
