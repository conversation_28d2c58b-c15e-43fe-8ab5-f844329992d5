import { DialogTitle } from "@headlessui/react";

import { ActionButtons, ButtonProps } from "./ActionButtons";
import Modal from "./Modal";

export type ErrorMessage = {
  title?: string;
  message?: string;
};

type ErrorModalProps = {
  isOpen: boolean;
  closeModal: () => void;
  errorMessage: ErrorMessage | null;
  primaryButton?: ButtonProps;
  secondaryButton?: ButtonProps;
};

export default function ErrorMessageModal({
  isOpen,
  closeModal,
  errorMessage,
  secondaryButton,
  primaryButton,
}: ErrorModalProps) {
  const defaultPrimaryButton: ButtonProps = {
    label: "OK",
    onClick: () => closeModal(),
    variant: "primary",
  };

  return (
    <Modal
      title={``}
      isOpen={isOpen}
      closeModal={closeModal}
      widthClass="w-11/12 sm:w-10/12 md:w-5/12 lg:w-4/12"
    >
      <div className="items-center">
        <DialogTitle
          as="h3"
          className="flex flex-row items-center justify-between space-x-12 pb-4 text-xl font-semibold text-black dark:text-white"
        >
          {errorMessage?.title}
        </DialogTitle>
        <div className="dark:text-white">{errorMessage?.message}</div>
      </div>
      <div className="mt-4 flex justify-end space-x-2">
        <ActionButtons
          primaryButton={primaryButton || defaultPrimaryButton}
          secondaryButton={secondaryButton}
        />
      </div>
    </Modal>
  );
}
