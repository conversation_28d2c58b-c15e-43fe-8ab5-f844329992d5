import { createColumnHelper } from "@tanstack/react-table";
import { format } from "date-fns/format";
import { HiOutlineGlobe } from "react-icons/hi";

import { InventoryDomainResponse } from "../model";

const columnHelper = createColumnHelper<InventoryDomainResponse>();

// Helper function to get background color based on activity type
const getActivityTypeColor = (type: string | null): string => {
  if (!type) return "";

  switch (type.toUpperCase()) {
    case "DOMAIN_ASSIGNED":
      return "bg-green-50";
    case "DOMAIN_UNASSIGNED":
      return "";
    case "DOMAIN_BURNED":
      return "bg-red-50";
    case "DOMAIN_EXPIRED":
      return "bg-blue-50";
    case "DOMAIN_QUARANTINED":
      return "bg-yellow-50";
    default:
      return "";
  }
};

export const inventoryDomainHistoryColumns = [
  {
    id: "rowBackground",
    cell: () => null,
    meta: {
      getRowClassName: (row: any) => getActivityTypeColor(row.original.type),
    },
  },
  columnHelper.accessor("url", {
    header: () => "Domain",
    cell: (info) => (
      <div className="flex min-w-[120px] items-center space-x-1 py-2">
        <span className="flex-shrink-0">
          <HiOutlineGlobe className="h-4 w-4 text-blue-500" />
        </span>
        <span className="flex-1 truncate text-sm">
          {info.getValue() || "-"}
        </span>
      </div>
    ),
  }),
  columnHelper.accessor("registrar", {
    header: () => "Registrar",
    cell: (info) => (
      <div className="min-w-[100px] truncate py-2 text-sm">
        {info.getValue() || "-"}
      </div>
    ),
  }),

  columnHelper.accessor("engagement", {
    header: () => "Engagement",
    cell: (info) => (
      <div className="min-w-[80px] truncate py-2 text-sm">
        {info.getValue() || "-"}
      </div>
    ),
  }),
  columnHelper.accessor("client", {
    header: () => "Client",
    cell: (info) => (
      <div className="min-w-[60px] truncate py-2 text-sm">
        {info.getValue() || "-"}
      </div>
    ),
  }),
  columnHelper.accessor("type", {
    header: () => "Activity Type",
    cell: (info) => (
      <div className="min-w-[100px] truncate py-2 text-sm">
        {info.getValue() || "-"}
      </div>
    ),
  }),
  columnHelper.accessor("last_activity", {
    header: () => "Created At",
    cell: (info) => {
      const value = info.getValue();
      if (!value)
        return <div className="min-w-[100px] truncate py-2 text-sm">-</div>;

      // Handle different date formats
      let formattedDate = value;
      try {
        // If it's already in DD.MM.YYYY format, use it as is
        if (value.includes(".") && value.split(".").length === 3) {
          formattedDate = value;
        } else {
          // Otherwise, parse and format it to dd.mm.yyyy
          formattedDate = format(new Date(value), "dd.MM.yyyy");
        }
      } catch (error) {
        console.warn("Error formatting date:", value, error);
        formattedDate = value;
      }

      return (
        <div className="min-w-[100px] truncate py-2 text-sm">
          {formattedDate}
        </div>
      );
    },
    filterFn: "dateRange",
    meta: {
      filterVariant: "date",
    },
  }),
  columnHelper.accessor("username", {
    header: () => "Last User",
    cell: (info) => (
      <div className="min-w-[100px] truncate py-2 text-sm">
        {info.getValue() || "-"}
      </div>
    ),
  }),
];
