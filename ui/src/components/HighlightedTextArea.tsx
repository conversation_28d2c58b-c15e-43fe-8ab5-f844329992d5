import hljs from "highlight.js";
import "highlight.js/styles/atom-one-dark.css";
import React, { useEffect, useRef } from "react";

import { useTheme } from "../context/ThemeProvider";

interface HighlightedTextareaProps {
  value?: string;
  language?: string;
}

const HighlightedTextarea: React.FC<HighlightedTextareaProps> = ({
  value,
  language = "javascript",
}) => {
  const highlightedCodeRef = useRef<HTMLDivElement>(null);
  const { isDarkMode } = useTheme();

  useEffect(() => {
    if (highlightedCodeRef.current) {
      highlightedCodeRef.current.innerHTML = "";

      const codeBlock = document.createElement("pre");
      const codeElement = document.createElement("code");

      codeElement.className = `language-${language}`;
      codeElement.textContent = value || "";

      codeBlock.appendChild(codeElement);
      highlightedCodeRef.current.appendChild(codeBlock);

      hljs.highlightElement(codeElement);
    }
  }, [value, isDarkMode, language]);

  // const handleTextareaChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
  //   onChange(e.target.value);
  // };

  /* <textarea
        className="w-full whitespace-pre-wrap break-words rounded-sm border border-solid border-gray-400 p-4 text-base font-normal blur-none focus:outline-hidden focus:ring-2 focus:ring-purple-700 dark:border-transparent dark:bg-slate-600 dark:text-slate-100"
        value={value}
        rows={4}
        onChange={handleTextareaChange}
      /> */

  return (
    <div
      className="max-h-[50vh] w-full overflow-y-auto rounded-sm bg-transparent p-3 text-wrap break-words whitespace-pre-wrap blur-none"
      ref={highlightedCodeRef}
      style={{
        fontFamily: "Courier New, Courier, monospace",
        fontSize: "14px",
        textShadow: "none",
        opacity: 100,
      }}
    />
  );
};

export default HighlightedTextarea;
