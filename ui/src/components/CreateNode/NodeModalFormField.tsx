import { Chips, ChipsChangeEvent } from "primereact/chips";
import React, { ChangeEvent } from "react";

interface Option {
  value: string;
  label: string;
}

interface NodeModalFormFieldProps {
  label: string;
  required?: boolean;
  type: string;
  value: string | string[];
  onChange: (
    e:
      | ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
      | ChipsChangeEvent
      | string[],
  ) => void;
  options?: Option[];
  errorMessage: string | null;
}

const NodeModalFormField: React.FC<NodeModalFormFieldProps> = ({
  label,
  required,
  type,
  value,
  onChange,
  options = [],
  errorMessage,
}) => {
  return (
    <div className="flex flex-col">
      <label className="mb-1 flex flex-row space-x-1">
        <span className="font-semibold">{label}</span>
        {required && <span className="text-red-600">*</span>}
      </label>
      {type === "select" && (
        <select
          className="w-full rounded-sm border border-solid border-gray-400 px-4 py-2.5 text-gray-700 hover:bg-purple-50 focus:ring-2 focus:ring-purple-700 focus:outline-hidden dark:border-transparent dark:bg-slate-600 dark:text-slate-100"
          value={value as string}
          onChange={(e) => onChange(e)}
        >
          {options.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
      )}
      {type === "chips" && (
        <div className="p-fluid mb-0">
          <Chips
            className="rounded-md border border-gray-400 py-1 text-gray-800 focus:ring-purple-700 dark:border-transparent dark:bg-slate-600 dark:text-slate-100"
            value={value as string[]}
            onChange={(e) => onChange(e)}
          />
          {errorMessage && (
            <span className="mt-1 text-red-600">{errorMessage}</span>
          )}
        </div>
      )}
      {type === "text" && (
        <>
          <input
            className="w-full rounded-sm border border-solid border-gray-400 px-4 py-2.5 text-gray-700 focus:ring-2 focus:ring-purple-700 focus:outline-hidden dark:border-transparent dark:bg-slate-600 dark:text-slate-100"
            type="text"
            value={value as string}
            onChange={(e) => onChange(e)}
          />
          {errorMessage && (
            <span className="mt-1 text-red-600">{errorMessage}</span>
          )}
        </>
      )}
      {type === "textarea" && (
        <textarea
          className="w-full border border-solid border-blue-500 bg-gray-100 p-4 text-base font-normal dark:border-transparent dark:bg-slate-600 dark:text-slate-100"
          value={value as string}
          onChange={(e) => onChange(e)}
          rows={4}
        />
      )}
    </div>
  );
};

export default NodeModalFormField;
