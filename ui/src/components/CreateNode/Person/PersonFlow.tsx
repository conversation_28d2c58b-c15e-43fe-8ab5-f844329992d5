import { useQueryClient } from "@tanstack/react-query";
import { useState } from "react";
import { toast } from "react-toastify";

import {
  PostNodesPersonMutationBody,
  getGetEngagementGraphsQueryKey,
  getGetEngagementQueryKey,
  usePostNodes<PERSON>erson,
} from "../../../client.ts";
import { validationSchemaType } from "../../../utils/validationutils.ts";
import ErrorMessageModal, { ErrorMessage } from "../../ErrorMessageModal.tsx";
import CreateNodeTypeModal, { Errors } from "../CreateNodeTypeModal.tsx";
import { CreateNodeScreen, ModalProps } from "../types.ts";

export default function PersonFlow({
  engagementID,
  nodeGroupID,
  setCreateNodeScreen,
  closeModal,
  setNodeType,
}: ModalProps) {
  const queryClient = useQueryClient();
  const [errors, setErrors] = useState<Errors>({});
  const [isOpenErrorModal, setIsOpenErrorModal] = useState<boolean>(false);
  const [errorMessage, setErrorMessage] = useState<ErrorMessage | null>(null);

  const fields = [
    {
      name: "first_name",
      label: "First Name",
      type: "text",
      required: true,
      validator: validationSchemaType.name,
    },
    {
      name: "last_name",
      label: "Last Name",
      type: "text",
      required: false,
      validator: validationSchemaType.name,
    },
    {
      name: "email",
      label: "Email",
      type: "text",
      required: false,
      validator: validationSchemaType.email,
    },
    {
      name: "company",
      label: "Company",
      type: "text",
      required: false,
      validator: validationSchemaType.companyName,
    },
    {
      name: "title",
      label: "Title",
      type: "text",
      required: false,
      validator: validationSchemaType.title,
    },
  ];

  const initialValues = {
    first_name: "",
    last_name: "",
    email: "",
    company: "",
    title: "",
  };

  const validationSchema = fields.map((field) => ({
    name: field.name,
    required: field.required,
    validator: field.validator,
  }));

  const createPersonDeploymentMutation = usePostNodesPerson({
    mutation: {
      onError: () => {
        openErrorModal({
          title: "Error creating node instance.",
          message: "Please try again later.",
        });
      },
      onSettled: (_data: any, error: unknown) => {
        if (!error) {
          toast.success("Node instance has been successfully created.");
        }
        const queryKey = getGetEngagementGraphsQueryKey(engagementID);
        queryClient.invalidateQueries({ queryKey });
        const engagementQueryKey = getGetEngagementQueryKey(engagementID);
        queryClient.invalidateQueries({ queryKey: engagementQueryKey });
        closeModal();
      },
    },
  });

  const handleSubmit = (formData: PostNodesPersonMutationBody) => {
    const data = {
      first_name: formData.first_name,
      ...(formData.last_name && { last_name: formData.last_name }),
      ...(formData.email && { email: formData.email }),
      ...(formData.company && { company: formData.company }),
      ...(formData.title && { title: formData.title }),
      engagement_id: engagementID,
      node_group_id: nodeGroupID,
    };

    createPersonDeploymentMutation.mutate({ data });
    setNodeType(null);
  };

  function openErrorModal(errorMessage: ErrorMessage) {
    if (errorMessage) {
      setErrorMessage(errorMessage);
    }
    setIsOpenErrorModal(true);
  }

  function closeErrorModal() {
    setIsOpenErrorModal(false);
  }

  return (
    <>
      <CreateNodeTypeModal
        fields={fields}
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
        closeModal={() => {
          closeModal();
        }}
        setNodeType={setNodeType}
        setCreateNodeScreen={setCreateNodeScreen}
        CreateNodeScreen={CreateNodeScreen}
        errors={errors}
        setErrors={setErrors}
      />
      <ErrorMessageModal
        isOpen={isOpenErrorModal}
        closeModal={closeErrorModal}
        errorMessage={errorMessage}
      />
    </>
  );
}
