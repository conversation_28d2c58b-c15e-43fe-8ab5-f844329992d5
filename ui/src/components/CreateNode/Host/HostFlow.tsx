import { useQueryClient } from "@tanstack/react-query";
import { useState } from "react";
import { toast } from "react-toastify";

import {
  PostNodesHostMutationBody,
  getGetEngagementGraphsQueryKey,
  getGetEngagementQueryKey,
  usePostNodesHost,
} from "../../../client.ts";
import "../../../index.css";
import { validationSchemaType } from "../../../utils/validationutils.ts";
import ErrorMessageModal, { ErrorMessage } from "../../ErrorMessageModal.tsx";
import CreateNodeTypeModal, { Errors } from "../CreateNodeTypeModal.tsx";
import { CreateNodeScreen, ModalProps } from "../types.ts";

export default function HostFlow({
  engagementID,
  nodeGroupID,
  setCreateNodeScreen,
  closeModal,
  setNodeType,
}: ModalProps) {
  const queryClient = useQueryClient();
  const [errors, setErrors] = useState<Errors>({});
  const [isOpenErrorModal, setIsOpenErrorModal] = useState<boolean>(false);
  const [errorMessage, setErrorMessage] = useState<ErrorMessage | null>(null);

  const fields = [
    {
      name: "name",
      label: "Name",
      type: "text",
      required: true,
      validator: validationSchemaType.name,
    },
    {
      name: "ip_addresses",
      label: "IP Addresses",
      type: "chips",
      required: false,
      validator: validationSchemaType.ipAddress,
    },
    {
      name: "alternative_names",
      label: "Alternative Names",
      type: "chips",
      required: false,
      validator: validationSchemaType.alternativeNames,
    },
  ];

  const initialValues = {
    name: "",
    ipAddresses: [],
    alternativeNames: [],
  };

  const validationSchema = fields.map((field) => ({
    name: field.name,
    required: field.required,
    validator: field.validator,
  }));

  const createHostDeploymentMutation = usePostNodesHost({
    mutation: {
      onError: () => {
        openErrorModal({
          title: "Error creating node instance.",
          message: "Please try again later.",
        });
      },
      onSettled: (_data: any, error: unknown) => {
        if (!error) {
          toast.success("Node instance has been successfully created.");
        }
        const queryKey = getGetEngagementGraphsQueryKey(engagementID);
        queryClient.invalidateQueries({ queryKey });
        const engagementQueryKey = getGetEngagementQueryKey(engagementID);
        queryClient.invalidateQueries({ queryKey: engagementQueryKey });
        closeModal();
        setNodeType(null);
      },
    },
  });

  const handleSubmit = (formData: PostNodesHostMutationBody) => {
    createHostDeploymentMutation.mutate({
      data: {
        name: formData.name,
        ip_addresses: formData.ip_addresses,
        alternative_names: formData.alternative_names,
        engagement_id: engagementID,
        node_group_id: nodeGroupID,
      },
    });
  };

  function openErrorModal(errorMessage: ErrorMessage) {
    if (errorMessage) {
      setErrorMessage(errorMessage);
    }
    setIsOpenErrorModal(true);
  }

  function closeErrorModal() {
    setIsOpenErrorModal(false);
  }

  return (
    <>
      <CreateNodeTypeModal
        fields={fields}
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
        closeModal={() => {
          closeModal();
        }}
        setNodeType={setNodeType}
        setCreateNodeScreen={setCreateNodeScreen}
        CreateNodeScreen={CreateNodeScreen}
        errors={errors}
        setErrors={setErrors}
      />
      <ErrorMessageModal
        isOpen={isOpenErrorModal}
        closeModal={closeErrorModal}
        errorMessage={errorMessage}
      />
    </>
  );
}
