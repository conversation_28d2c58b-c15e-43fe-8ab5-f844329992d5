export type CloudProvider = {
  option: string;
  description: string;
};

export const cloudProviders: CloudProvider[] = [
  { option: "AWS", description: "Amazon Web Services (AWS)" },
  { option: "GCP", description: "Google Cloud Platform (GCP)" },
  { option: "AZURE", description: "Microsoft Azure" },
];

export type AmiAws = {
  image_id: string;
  name: string;
  description: string;
  creation_date: string;
};

export type AmiAzure = {
  name: string;
  description: string;
  image_id: string; // maps to version
  publisher: string;
  offer: string;
  sku: string;
  version: string;
};

export type CloudInstanceType = {
  alias: string;
  type: string;
};
