import { ChipsChangeEvent } from "primereact/chips";
import React, { ChangeEvent, useState } from "react";
import { HiChevronLeft } from "react-icons/hi";
import { Zod<PERSON>rray, ZodOptional, ZodString } from "zod";

import { getZodErrorMessage } from "../../utils/zodHelpers";
import { ActionButtons, ButtonProps, EventButtonProps } from "../ActionButtons";
import NodeModalFormField from "./NodeModalFormField";
import { CreateNodeScreen, NodeType } from "./types";

interface Field {
  name: string;
  label: string;
  type: string;
  required?: boolean;
  options?: { value: string; label: string }[];
}

interface FormValues {
  [key: string]: string | number | boolean | string[] | number[];
}

export interface Errors {
  [key: string]: string | null;
}

interface CreateNodeTypeModalProps<FormData> {
  fields: Field[];
  initialValues: FormValues;
  validationSchema: {
    name: string;
    required: boolean;
    validator:
      | ZodString
      | ZodOptional<ZodString>
      | ZodOptional<ZodArray<ZodString>>;
  }[];
  onSubmit: (formData: FormData) => void;
  closeModal: () => void;
  setCreateNodeScreen: (screen: CreateNodeScreen) => void;
  setNodeType: (nodeType: NodeType | null) => void;
  CreateNodeScreen: typeof CreateNodeScreen;
  errors: Errors;
  setErrors: React.Dispatch<React.SetStateAction<Errors>>;
  renderCustomField?: (
    field: any,
    value: string | string[] | number | boolean | number[],
    onChange: (
      e:
        | string
        | string[]
        | ChangeEvent<
            HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement
          >
        | ChipsChangeEvent,
    ) => void,
    error?: string,
  ) => React.ReactNode;
}

const CreateNodeTypeModal = <FormData,>({
  fields,
  initialValues,
  validationSchema,
  onSubmit,
  closeModal,
  setCreateNodeScreen,
  setNodeType,
  CreateNodeScreen,
  errors,
  setErrors,
  renderCustomField,
}: CreateNodeTypeModalProps<FormData>) => {
  const [formValues, setFormValues] = useState(initialValues);

  const handleChange =
    (field: string) =>
    (
      e:
        | string
        | string[]
        | ChangeEvent<
            HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement
          >
        | ChipsChangeEvent,
    ) => {
      const value: string | string[] =
        typeof e === "string"
          ? e
          : Array.isArray(e)
            ? e
            : (e.target.value ?? "");
      setFormValues({
        ...formValues,
        [field]: value as FormValues[keyof FormValues],
      });
      const validator = validationSchema.find(
        (schema) => schema.name === field,
      )?.validator;
      if (validator) {
        validateField(field, value, validator);
      }
    };

  const validateField = (
    name: string,
    value: string | string[],
    validator: any,
  ) => {
    const validationResult = validator.safeParse(value);
    if (!validationResult.success) {
      setErrors((prev) => ({
        ...prev,
        [name]: getZodErrorMessage(validationResult.error),
      }));
      return false;
    } else {
      setErrors((prev) => ({
        ...prev,
        [name]: "",
      }));
      return true;
    }
  };

  const areInputsValid = () => {
    const isValid = validationSchema.every((field) => {
      let value;
      if (Array.isArray(formValues[field.name])) {
        value = formValues[field.name];
      } else {
        value = formValues[field.name]?.toString();
      }

      if (field.required && !value) {
        return false;
      }

      if (field.required || value) {
        const validationResult = field.validator.safeParse(value);
        return validationResult.success;
      }

      return true;
    });

    return isValid;
  };

  const handleSubmit = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    const isValid = areInputsValid();
    if (isValid) {
      onSubmit(formValues as unknown as FormData);
      setCreateNodeScreen(CreateNodeScreen.SelectNodeType);
    }
  };

  const isSingleField = fields.length === 1;

  const primaryButton: EventButtonProps = {
    label: "Create",
    onClick: handleSubmit,
    variant: "primary",
    disabled: !areInputsValid(),
  };

  const secondaryButton: ButtonProps = {
    label: "Cancel",
    onClick: () => {
      closeModal();
      setCreateNodeScreen(CreateNodeScreen.SelectNodeType);
      setNodeType(null);
    },
    variant: "secondary",
  };

  return (
    <div className="flex w-full flex-col rounded-lg md:max-w-5xl md:p-4 xl:p-8">
      <div
        className={`grid ${isSingleField ? "grid-cols-1" : "lg:grid-cols-2"} gap-2 pt-4 lg:gap-6`}
      >
        {fields.map((field) =>
          field.type === "custom" && renderCustomField ? (
            <div key={field.name}>
              {renderCustomField(
                field,
                formValues[field.name],
                handleChange(field.name),
                errors[field.name] || undefined,
              )}
            </div>
          ) : (
            <NodeModalFormField
              key={field.name}
              label={field.label}
              required={field.required}
              type={field.type}
              value={formValues[field.name] as string | string[]}
              onChange={handleChange(field.name)}
              options={field.options || []}
              errorMessage={errors[field.name]}
            />
          ),
        )}
      </div>
      <div
        id="node-selection-actions"
        className="mt-8 flex flex-col items-center justify-between md:flex-row"
      >
        <button
          className="flex hidden flex-row items-center space-x-2 text-gray-600 hover:text-gray-800 md:flex"
          onClick={() => {
            setCreateNodeScreen(CreateNodeScreen.SelectNodeType);
            setNodeType(null);
          }}
        >
          <HiChevronLeft className="h-4 w-4 dark:text-white" />
          <span className="dark:text-white">Back</span>
        </button>
        <div className="flex w-full flex-row items-center justify-end space-x-4 align-middle md:w-auto">
          <ActionButtons
            primaryButton={primaryButton}
            secondaryButton={secondaryButton}
          />
        </div>
      </div>
    </div>
  );
};

export default CreateNodeTypeModal;
