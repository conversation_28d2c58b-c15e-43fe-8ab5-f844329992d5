import {
  Combobox,
  ComboboxButton,
  ComboboxInput,
  ComboboxOption,
  ComboboxOptions,
} from "@headlessui/react";
import { useQueryClient } from "@tanstack/react-query";
import { ChipsChangeEvent } from "primereact/chips";
import { ChangeEvent, useState } from "react";
import { HiChevronDown, HiPlus } from "react-icons/hi";
import { toast } from "react-toastify";

import {
  PostNodesUrlMutationBody,
  getGetEngagementGraphsQueryKey,
  getGetEngagementQueryKey,
  getGetInventoryDomainsQueryKey,
  useGetInventoryDomains,
  usePostNodesUrl,
} from "../../../client.ts";
import "../../../index.css";
import { validationSchemaType } from "../../../utils/validationutils.ts";
import ErrorMessageModal, { ErrorMessage } from "../../ErrorMessageModal.tsx";
import CreateNodeTypeModal, { Errors } from "../CreateNodeTypeModal.tsx";
import { CreateNodeScreen, ModalProps } from "../types.ts";

export default function URLFlow({
  engagementID,
  nodeGroupID,
  setCreateNodeScreen,
  closeModal,
  setNodeType,
}: ModalProps) {
  const queryClient = useQueryClient();
  const [errors, setErrors] = useState<Errors>({});
  const [isOpenErrorModal, setIsOpenErrorModal] = useState<boolean>(false);
  const [errorMessage, setErrorMessage] = useState<ErrorMessage | null>(null);
  const [urlQuery, setUrlQuery] = useState("");
  const [selectedUrl, setSelectedUrl] = useState("");

  // Fetch existing domains
  const { data: domainsList } = useGetInventoryDomains({
    query: {
      queryKey: getGetInventoryDomainsQueryKey(),
    },
  });

  // Filter domains based on search query
  const filteredDomains =
    urlQuery === ""
      ? domainsList?.domains || []
      : domainsList?.domains?.filter((domain) =>
          domain.url.toLowerCase().includes(urlQuery.toLowerCase()),
        ) || [];

  // Check if the current query doesn't match any existing domains
  const isNewUrl =
    urlQuery !== "" &&
    !filteredDomains.some(
      (domain) => domain.url.toLowerCase() === urlQuery.toLowerCase(),
    );

  const fields = [
    {
      name: "url",
      label: "URL",
      type: "custom", // Custom field type for our dropdown
      required: true,
      validator: validationSchemaType.url,
    },
  ];

  const initialValues = {
    url: "",
  };

  const validationSchema = fields.map((field) => ({
    name: field.name,
    required: field.required,
    validator: field.validator,
  }));

  const createUrlDeploymentMutation = usePostNodesUrl({
    mutation: {
      onError: () => {
        openErrorModal({
          title: "Error creating node instance.",
          message: "Please try again later.",
        });
      },
      onSettled: (_data: any, error: unknown) => {
        if (!error) {
          toast.success("Node instance has been successfully created.");
        }
        const queryKey = getGetEngagementGraphsQueryKey(engagementID);
        queryClient.invalidateQueries({ queryKey });
        const engagementQueryKey = getGetEngagementQueryKey(engagementID);
        queryClient.invalidateQueries({ queryKey: engagementQueryKey });
        closeModal();
        setNodeType(null);
      },
    },
  });

  const handleSubmit = (formData: PostNodesUrlMutationBody) => {
    createUrlDeploymentMutation.mutate({
      data: {
        url: formData.url,
        engagement_id: engagementID,
        node_group_id: nodeGroupID,
      },
    });
  };

  function openErrorModal(errorMessage: ErrorMessage) {
    if (errorMessage) {
      setErrorMessage(errorMessage);
    }
    setIsOpenErrorModal(true);
  }

  function closeErrorModal() {
    setIsOpenErrorModal(false);
  }

  // Custom renderer for the URL field
  const renderCustomField = (
    field: any,
    _value: string | string[] | number | boolean | number[],
    onChange: (
      e:
        | string
        | string[]
        | ChangeEvent<
            HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement
          >
        | ChipsChangeEvent,
    ) => void,
    error?: string,
  ) => {
    if (field.name === "url") {
      return (
        <div className="w-full">
          <Combobox
            value={selectedUrl}
            onChange={(newValue: string) => {
              setSelectedUrl(newValue);

              // Ensure URL has protocol for validation
              const formattedUrl =
                newValue &&
                !newValue.startsWith("http://") &&
                !newValue.startsWith("https://")
                  ? `https://${newValue}`
                  : newValue;

              // Update the form value through the provided onChange
              onChange(formattedUrl);
            }}
          >
            <div className="relative mt-1">
              <div className="focus-visible:ring-opacity-75 relative w-full cursor-default overflow-hidden rounded-lg border border-gray-300 bg-white text-left focus:outline-none focus-visible:ring-2 focus-visible:ring-white focus-visible:ring-offset-2 focus-visible:ring-offset-purple-300 sm:text-sm">
                <ComboboxInput
                  className="w-full border-none py-2 pr-10 pl-3 text-sm leading-5 text-gray-900 focus:ring-0"
                  placeholder="Find or add a URL..."
                  displayValue={() => selectedUrl}
                  onChange={(event) => {
                    const inputValue = event.target.value;
                    setUrlQuery(inputValue);
                    setSelectedUrl(inputValue);

                    // Format URL with https:// if needed
                    const formattedUrl =
                      inputValue &&
                      !inputValue.startsWith("http://") &&
                      !inputValue.startsWith("https://")
                        ? `https://${inputValue}`
                        : inputValue;

                    // Update form values
                    onChange(formattedUrl);
                  }}
                />
                <ComboboxButton className="absolute inset-y-0 right-0 flex items-center pr-2">
                  <HiChevronDown
                    className="h-5 w-5 text-gray-400"
                    aria-hidden="true"
                  />
                </ComboboxButton>
              </div>
              <ComboboxOptions
                className="ring-opacity-5 absolute z-[9999] mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black focus:outline-none sm:text-sm"
                style={{ position: "absolute", zIndex: 9999 }}
              >
                {isNewUrl && (
                  <ComboboxOption
                    key="new-url-option"
                    value={urlQuery}
                    className={({ active }) =>
                      `relative cursor-pointer py-2 pr-4 pl-10 select-none ${
                        active ? "bg-purple-600 text-white" : "text-gray-900"
                      }`
                    }
                  >
                    {({ selected }) => (
                      <>
                        <span
                          className={`block truncate ${selected ? "font-medium" : "font-normal"}`}
                        >
                          <HiPlus className="mr-2 inline-block" /> Add '
                          {urlQuery}' as new URL
                        </span>
                      </>
                    )}
                  </ComboboxOption>
                )}

                {filteredDomains.map((domain) => (
                  <ComboboxOption
                    key={domain.id}
                    value={domain.url}
                    className={({ active }) =>
                      `relative cursor-pointer py-2 pr-4 pl-10 select-none ${
                        active ? "bg-purple-600 text-white" : "text-gray-900"
                      }`
                    }
                  >
                    {({ selected }) => (
                      <>
                        <span
                          className={`block truncate ${selected ? "font-medium" : "font-normal"}`}
                        >
                          {domain.url}
                        </span>
                      </>
                    )}
                  </ComboboxOption>
                ))}

                {filteredDomains.length === 0 && !isNewUrl && (
                  <div
                    key="no-results"
                    className="relative cursor-default px-4 py-2 text-gray-700 select-none"
                  >
                    No URLs found.
                  </div>
                )}
              </ComboboxOptions>
            </div>
          </Combobox>
          {error && <p className="mt-1 text-sm text-red-600">{error}</p>}
        </div>
      );
    }
    return null;
  };

  return (
    <>
      <CreateNodeTypeModal
        fields={fields}
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
        closeModal={() => {
          closeModal();
        }}
        setNodeType={setNodeType}
        setCreateNodeScreen={setCreateNodeScreen}
        CreateNodeScreen={CreateNodeScreen}
        errors={errors}
        setErrors={setErrors}
        renderCustomField={renderCustomField}
      />
      <ErrorMessageModal
        isOpen={isOpenErrorModal}
        closeModal={closeErrorModal}
        errorMessage={errorMessage}
      />
    </>
  );
}
