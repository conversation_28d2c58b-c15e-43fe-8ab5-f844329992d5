import { useState } from "react";
import { BsPlusSquare } from "react-icons/bs";

import CreateNodeModal from "./CreateNodeModal.tsx";

type Props = {
  engagementID: string;
};

export default function CreateNodeButton({ engagementID }: Props) {
  const [isOpen, setIsOpen] = useState<boolean>(false);

  function closeModal() {
    setIsOpen(false);
  }

  function openModal() {
    setIsOpen(true);
  }

  return (
    <>
      <button
        type="button"
        onClick={openModal}
        className="flex items-center space-x-2 rounded-md bg-purple-700 px-6 py-3 text-sm font-medium whitespace-nowrap text-white hover:bg-purple-800"
      >
        <BsPlusSquare className="h-5 w-5" />
        <span>Create Node</span>
      </button>

      <CreateNodeModal
        engagementID={engagementID}
        isOpen={isOpen}
        closeModal={closeModal}
      />
    </>
  );
}
