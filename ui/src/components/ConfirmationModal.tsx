import { DialogTitle } from "@headlessui/react";

import { ActionButtons, ButtonProps } from "./ActionButtons";
import Modal from "./Modal";

interface ConfirmationModalProps {
  isOpen: boolean;
  closeModal: () => void;
  title: string;
  message: string;
  confirmButtonText: string;
  confirmButtonVariant?: "primary" | "danger";
  onConfirm: () => void;
  domainName?: string;
}

export default function ConfirmationModal({
  isOpen,
  closeModal,
  title,
  message,
  confirmButtonText,
  confirmButtonVariant = "primary",
  onConfirm,
  domainName,
}: ConfirmationModalProps) {
  const handleConfirm = () => {
    onConfirm();
    closeModal();
  };

  const primaryButton: ButtonProps = {
    label: confirmButtonText,
    onClick: handleConfirm,
    variant: confirmButtonVariant,
  };

  const secondaryButton: ButtonProps = {
    label: "Cancel",
    onClick: closeModal,
    variant: "secondary",
  };

  return (
    <Modal
      title=""
      isOpen={isOpen}
      closeModal={closeModal}
      widthClass="w-11/12 sm:w-10/12 md:w-5/12 lg:w-4/12"
    >
      <div className="flex flex-row justify-between">
        <DialogTitle
          as="h3"
          className="flex flex-row items-center justify-between space-x-12 pb-4 text-xl font-semibold text-black dark:text-slate-100"
        >
          <div>
            {title}
            {domainName && (
              <>
                <br />
                <span
                  className={`mt-2 ${confirmButtonVariant === "danger" ? "text-red-700 dark:text-red-500" : "text-blue-700 dark:text-blue-500"}`}
                >
                  {domainName}
                </span>
              </>
            )}
            <div className="mt-2 text-sm text-gray-600 dark:text-gray-400">
              {message}
            </div>
          </div>
        </DialogTitle>
      </div>
      <div className="mt-4 flex justify-end space-x-2">
        <ActionButtons
          primaryButton={primaryButton}
          secondaryButton={secondaryButton}
        />
      </div>
    </Modal>
  );
}
