import {
  Combobox,
  ComboboxButton,
  ComboboxInput,
  ComboboxOption,
  ComboboxOptions,
} from "@headlessui/react";
import { createColumnHelper } from "@tanstack/react-table";
import { useEffect, useState } from "react";
import { HiChevronDown, HiOutlineGlobe } from "react-icons/hi";
import { <PERSON><PERSON>heck, HiPencil, HiXMark } from "react-icons/hi2";

import { DomainResponse } from "../model";

const columnHelper = createColumnHelper<DomainResponse>();

// Inline editable cell component
const EditableCell = ({
  value,
  onSave,
  type = "text",
  icon,
}: {
  value: string;
  onSave: (newValue: string) => void;
  type?: "text" | "date";
  icon?: React.ReactNode;
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editValue, setEditValue] = useState(value);

  // Convert date from DD.MM.YYYY to YYYY-MM-DD for input
  const getInputValue = () => {
    if (type === "date" && value) {
      // Handle DD.MM.YYYY format
      if (value.includes(".")) {
        const parts = value.split(".");
        if (parts.length === 3 && parts[2].length === 4) {
          return `${parts[2]}-${parts[1].padStart(2, "0")}-${parts[0].padStart(2, "0")}`;
        }
      }
      // Handle ISO format (2025-04-30T11:20:25.393Z)
      else if (value.includes("T") || value.includes("Z")) {
        try {
          const date = new Date(value);
          if (!isNaN(date.getTime())) {
            const year = date.getFullYear();
            const month = (date.getMonth() + 1).toString().padStart(2, "0");
            const day = date.getDate().toString().padStart(2, "0");
            return `${year}-${month}-${day}`;
          }
        } catch (error) {
          console.log("Error parsing ISO date:", value, error);
        }
      }
      // Handle YYYY-MM-DD format (already correct for input)
      else if (value.includes("-") && value.split("-")[0].length === 4) {
        return value;
      }
    }
    return value;
  };

  // Convert date from YYYY-MM-DD to DD.MM.YYYY for display
  const getDisplayValue = (inputValue: string) => {
    if (type === "date" && inputValue && inputValue.includes("-")) {
      const parts = inputValue.split("-");
      if (parts.length === 3 && parts[0].length === 4) {
        // Only convert if it's in YYYY-MM-DD format
        return `${parts[2].padStart(2, "0")}.${parts[1].padStart(2, "0")}.${parts[0]}`;
      }
    }
    return inputValue;
  };

  const handleSave = () => {
    const displayValue = getDisplayValue(editValue);
    // Only save if the value actually changed
    if (displayValue !== value) {
      onSave(displayValue);
    } else {
      console.log("No change detected, skipping save");
    }
    setIsEditing(false);
  };

  const handleCancel = () => {
    setEditValue(getInputValue());
    setIsEditing(false);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleSave();
    } else if (e.key === "Escape") {
      handleCancel();
    }
  };

  if (isEditing) {
    return (
      <div className="flex min-w-[120px] items-center gap-2 py-2">
        {icon && <span className="flex-shrink-0">{icon}</span>}
        <input
          type={type}
          value={type === "date" ? getInputValue() : editValue}
          onChange={(e) => setEditValue(e.target.value)}
          onKeyDown={handleKeyDown}
          onBlur={handleSave}
          onFocus={() => {
            if (type === "date") {
              setEditValue(getInputValue());
            }
          }}
          autoFocus
          className="flex-1 rounded-md border border-gray-300 px-3 py-2 text-sm shadow-sm transition-colors focus:border-blue-500 focus:ring-2 focus:ring-blue-500 focus:outline-none dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:focus:border-blue-400 dark:focus:ring-blue-400"
        />
        <button
          onClick={handleSave}
          className="flex h-8 w-8 items-center justify-center rounded-md text-green-600 transition-colors hover:bg-green-50 hover:text-green-700 dark:hover:bg-green-900/20"
          title="Save changes"
        >
          <HiCheck className="h-4 w-4" />
        </button>
        <button
          onClick={handleCancel}
          className="flex h-8 w-8 items-center justify-center rounded-md text-red-600 transition-colors hover:bg-red-50 hover:text-red-700 dark:hover:bg-red-900/20"
          title="Cancel changes"
        >
          <HiXMark className="h-4 w-4" />
        </button>
      </div>
    );
  }

  return (
    <div className="group flex min-w-[120px] items-center gap-2 py-2">
      {icon && <span className="flex-shrink-0">{icon}</span>}
      <span className="flex-1 truncate text-sm text-gray-900 dark:text-gray-100">
        {value || "-"}
      </span>
      <button
        onClick={() => setIsEditing(true)}
        className="flex h-6 w-6 items-center justify-center rounded text-gray-400 opacity-0 transition-all group-hover:opacity-100 hover:bg-gray-100 hover:text-gray-600 dark:hover:bg-gray-700 dark:hover:text-gray-300"
        title="Edit field"
      >
        <HiPencil className="h-3.5 w-3.5" />
      </button>
    </div>
  );
};

// Editable dropdown cell component for registrar field
const EditableDropdownCell = ({
  value,
  onSave,
  options = [],
  icon,
}: {
  value: string;
  onSave: (newValue: string) => void;
  options?: string[];
  icon?: React.ReactNode;
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editValue, setEditValue] = useState(value);
  const [query, setQuery] = useState("");

  useEffect(() => {
    setEditValue(value);
  }, [value]);

  const handleSave = () => {
    // Only save if the value actually changed
    if (editValue !== value) {
      onSave(editValue);
    }
    setIsEditing(false);
    setQuery("");
  };

  const handleCancel = () => {
    setEditValue(value);
    setIsEditing(false);
    setQuery("");
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleSave();
    } else if (e.key === "Escape") {
      handleCancel();
    }
  };

  const filteredOptions = options.filter((option) =>
    option.toLowerCase().includes(query.toLowerCase()),
  );

  if (isEditing) {
    return (
      <div className="relative w-full min-w-[120px] py-2">
        {icon && (
          <span className="absolute top-1/2 left-0 z-10 flex-shrink-0 -translate-y-1/2 transform">
            {icon}
          </span>
        )}
        <div className="relative w-full">
          <Combobox
            value={editValue}
            onChange={(value: string | null) => setEditValue(value || "")}
          >
            <div className="relative">
              <ComboboxInput
                className={`w-full rounded-md border border-gray-300 px-3 py-2 text-sm shadow-sm transition-colors focus:border-blue-500 focus:ring-2 focus:ring-blue-500 focus:outline-none dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:focus:border-blue-400 dark:focus:ring-blue-400 ${icon ? "pl-8" : ""}`}
                onChange={(event) => {
                  setQuery(event.target.value);
                  setEditValue(event.target.value);
                }}
                onKeyDown={handleKeyDown}
                onBlur={handleSave}
                displayValue={(value: string) => value}
                placeholder="Enter registrar..."
                autoFocus
              />
              <div className="absolute inset-y-0 right-0 flex items-center">
                <ComboboxButton className="flex items-center pr-3">
                  <HiChevronDown
                    className="h-4 w-4 text-gray-400"
                    aria-hidden="true"
                  />
                </ComboboxButton>
                <button
                  onClick={handleCancel}
                  className="mr-1 flex h-8 w-8 items-center justify-center rounded-md text-red-600 transition-colors hover:bg-red-50 hover:text-red-700 dark:hover:bg-red-900/20"
                  title="Cancel changes"
                >
                  <HiXMark className="h-4 w-4" />
                </button>
              </div>
            </div>
            <ComboboxOptions className="ring-opacity-5 absolute z-[9999] mt-1 max-h-40 w-full overflow-auto rounded-md bg-white py-1 text-sm shadow-lg ring-1 ring-black focus:outline-none dark:bg-gray-800 dark:ring-gray-600">
              {filteredOptions.length > 0 ? (
                filteredOptions.map((option) => (
                  <ComboboxOption
                    key={option}
                    value={option}
                    className={({ focus }) =>
                      `relative cursor-pointer px-3 py-2 transition-colors select-none ${
                        focus
                          ? "bg-blue-600 text-white"
                          : "text-gray-900 hover:bg-gray-100 dark:text-gray-100 dark:hover:bg-gray-700"
                      }`
                    }
                  >
                    {option}
                  </ComboboxOption>
                ))
              ) : query !== "" ? (
                <div className="relative cursor-default px-3 py-2 text-gray-500 select-none dark:text-gray-400">
                  No registrars found.
                </div>
              ) : null}
            </ComboboxOptions>
          </Combobox>
        </div>
      </div>
    );
  }

  return (
    <div className="group flex min-w-[120px] items-center gap-2 py-2">
      {icon && <span className="flex-shrink-0">{icon}</span>}
      <span className="flex-1 truncate text-sm text-gray-900 dark:text-gray-100">
        {value || "-"}
      </span>
      <button
        onClick={() => setIsEditing(true)}
        className="flex h-6 w-6 items-center justify-center rounded text-gray-400 opacity-0 transition-all group-hover:opacity-100 hover:bg-gray-100 hover:text-gray-600 dark:hover:bg-gray-700 dark:hover:text-gray-300"
        title="Edit field"
      >
        <HiPencil className="h-3.5 w-3.5" />
      </button>
    </div>
  );
};

export const inventoryDomainColumns = ({
  onAssign,
  onBurn,
  onUpdate,
  onExpire,
  onAvailable,
  registrarOptions = [],
}: {
  onAssign?: (domain: DomainResponse) => void;
  onBurn?: (domainId: string) => void;
  onUpdate?: (
    domainId: string,
    field: keyof DomainResponse,
    value: string,
    domainStatus?: string,
  ) => void;
  onExpire?: (domainId: string) => void;
  onAvailable?: (
    domainId: string,
    field: keyof DomainResponse,
    value: string,
    domainStatus?: string,
  ) => void;
  registrarOptions?: string[];
} = {}) => [
  columnHelper.accessor("url", {
    header: () => "Domain",
    cell: (info) => (
      <EditableCell
        value={info.getValue()}
        onSave={(newValue) => onUpdate?.(info.row.original.id, "url", newValue)}
        icon={<HiOutlineGlobe className="h-4 w-4 text-blue-500" />}
      />
    ),
  }),
  columnHelper.accessor("registrar", {
    header: () => "Registrar",
    cell: (info) => (
      <EditableDropdownCell
        value={info.getValue() || ""}
        onSave={(newValue) =>
          onUpdate?.(info.row.original.id, "registrar", newValue)
        }
        options={registrarOptions}
      />
    ),
  }),
  columnHelper.accessor("purchase_date", {
    header: () => "Purchase Date",
    cell: (info) => (
      <EditableCell
        value={info.getValue() || ""}
        onSave={(newValue) =>
          onUpdate?.(info.row.original.id, "purchase_date", newValue)
        }
        type="date"
      />
    ),
    filterFn: "dateRange",
    meta: {
      filterVariant: "date",
    },
  }),
  columnHelper.accessor("renewal_date", {
    header: () => "Renewal Date",
    cell: (info) => (
      <EditableCell
        value={info.getValue() || ""}
        onSave={(newValue) =>
          onUpdate?.(info.row.original.id, "renewal_date", newValue)
        }
        type="date"
      />
    ),
    filterFn: "dateRange",
    meta: {
      filterVariant: "date",
    },
  }),
  columnHelper.accessor("domain_status_enum", {
    header: () => "Status",
    cell: (info) => {
      const status = info.getValue();
      let statusClass = "";

      switch (status) {
        case "ASSIGNED":
          statusClass = "bg-green-100 text-green-800";
          break;
        case "UNASSIGNED":
          statusClass = "bg-gray-100 text-gray-800";
          break;
        case "BURNED":
          statusClass = "bg-red-100 text-red-800";
          break;
        case "QUARANTINE":
          statusClass = "bg-yellow-100 text-yellow-800";
          break;
        case "EXPIRED":
          statusClass = "bg-blue-100 text-blue-800";
          break;
      }

      return (
        <div className="min-w-[70px] py-2">
          <span
            className={`rounded-full px-2 py-1 text-xs font-medium ${statusClass}`}
          >
            {status}
          </span>
        </div>
      );
    },
  }),
  columnHelper.accessor("engagement", {
    header: () => "Engagement",
    cell: (info) => (
      <div className="min-w-[80px] truncate py-2 text-sm">
        {info.getValue()}
      </div>
    ),
  }),
  columnHelper.accessor("client", {
    header: () => "Client",
    cell: (info) => (
      <div className="min-w-[60px] truncate py-2 text-sm">
        {info.getValue()}
      </div>
    ),
  }),
  columnHelper.accessor("age", {
    header: () => "Age",
    cell: (info) => (
      <div className="min-w-[50px] py-2 text-sm">{info.getValue()}</div>
    ),
  }),
  columnHelper.accessor((row) => row.id, {
    id: "actions",
    header: () => "Actions",
    cell: (info) => {
      const domain = info.row.original;
      const status = domain.domain_status_enum || "UNASSIGNED";

      return (
        <div className="flex space-x-2 py-2 whitespace-nowrap">
          {status === "UNASSIGNED" && (
            <>
              <button
                onClick={() => onAssign?.(domain)}
                className="rounded bg-blue-500 px-3 py-1 text-xs text-white hover:bg-blue-600"
                disabled={!onAssign}
              >
                Assign
              </button>
            </>
          )}
          {status === "QUARANTINE" && (
            <>
              <button
                onClick={() =>
                  onAvailable?.(domain.id, "domain_status_enum", "UNASSIGNED")
                }
                className="hover:bg-green-400-600 rounded bg-green-600 px-3 py-1 text-xs text-white"
                disabled={!onAvailable}
              >
                Release
              </button>
              <button
                onClick={() => onBurn?.(domain.id)}
                className="rounded bg-red-700 px-3 py-1 text-xs text-white hover:bg-red-800"
                disabled={!onBurn}
              >
                Burn
              </button>
            </>
          )}
          {status === "BURNED" && (
            <button
              onClick={() => onExpire?.(domain.id)}
              className="rounded bg-yellow-500 px-3 py-1 text-xs text-white hover:bg-yellow-600"
              disabled={!onExpire}
            >
              Expire
            </button>
          )}
        </div>
      );
    },
  }),
];
