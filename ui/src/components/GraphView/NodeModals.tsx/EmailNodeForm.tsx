import { ChangeEvent, useState } from "react";
import { useEffect } from "react";

import { EditNodesEmailAddressMutationBody } from "../../../client";
import { NodeEmailAddress } from "../../../model";
import {
  nodeValidationSchemas,
  validateFormData,
} from "../../../utils/validationutils";
import { ActionButtons, ButtonProps } from "../../ActionButtons";

interface EmailNodeFormProps {
  nodeData: NodeEmailAddress;
  isEditMode: boolean;
  setIsEditMode: (isEditMode: boolean) => void;
  onUpdate: (formData: EditNodesEmailAddressMutationBody) => void;
}

export function EmailNodeForm({
  nodeData,
  isEditMode,
  setIsEditMode,
  onUpdate,
}: EmailNodeFormProps) {
  const [formData, setFormData] = useState({
    email_address: "",
  });

  useEffect(() => {
    if (nodeData) {
      setFormData({
        email_address: nodeData.email_address || "",
      });
      // Clear errors when new data is loaded
      setError("");
    }
  }, [nodeData]);

  const handleSubmit = () => {
    // Validate form data only when save is clicked
    const { isValid, errors } = validateFormData(
      formData,
      nodeValidationSchemas.email_address,
    );

    setError(errors.email_address || "");

    if (isValid) {
      onUpdate(formData);
    }
  };

  const [error, setError] = useState("");

  const primaryButton: ButtonProps = {
    label: "Save",
    onClick: handleSubmit,
    variant: "primary",
  };

  const secondaryButton: ButtonProps = {
    label: "Cancel",
    onClick: () => {
      setIsEditMode(false);
    },
    variant: "secondary",
  };

  const handleInputChange = (e: ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prevData) => ({
      ...prevData,
      [name]: value,
    }));
    // Clear error when user starts typing (optional UX improvement)
    if (error) {
      setError("");
    }
  };

  return (
    <div
      className={`${isEditMode ? "flex-col" : "flex-row justify-between"} flex w-full md:w-5/6`}
    >
      <label className="font-semibold dark:text-slate-300">
        Email Address:
      </label>
      {isEditMode ? (
        <>
          <input
            type="text"
            className="mt-2 rounded-sm border border-solid border-gray-400 px-4 py-2 text-gray-700 focus:text-black focus:ring-2 focus:ring-purple-700 focus:outline-hidden dark:border-transparent dark:bg-slate-600 dark:text-slate-100 dark:focus:ring-purple-400"
            value={formData?.email_address}
            onChange={handleInputChange}
            name="email_address"
          />
          {error && <p className="mt-1 text-red-500">{error}</p>}
        </>
      ) : (
        <p className="dark:text-white">{nodeData?.email_address}</p>
      )}
      {isEditMode && (
        <div className="mt-8 flex justify-end">
          <ActionButtons
            primaryButton={primaryButton}
            secondaryButton={secondaryButton}
          />
        </div>
      )}
    </div>
  );
}
