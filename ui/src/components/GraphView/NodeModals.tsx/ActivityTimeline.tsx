import { format } from "date-fns";

import { ActivityLog } from "../../../model";

type ActivityTimelineProps = {
  data: ActivityLog[];
  selectedName: string;
};

export default function ActivityTimeline({ data }: ActivityTimelineProps) {
  return (
    <>
      {data?.map((entry: ActivityLog, i: number) => (
        <div className="mt-2 flex flex-col" key={i}>
          <div className="grid grid-cols-3 gap-2">
            <div className="font-semibold dark:text-white">User</div>
            <div className="col-span-2 dark:text-slate-400">
              {entry.username}
            </div>
          </div>
          <div className="grid grid-cols-3 gap-2">
            <div className="font-semibold dark:text-white">Date / Time</div>
            <div className="col-span-2 dark:text-slate-400">
              {format(new Date(entry.created_at), "dd-MMM-yyyy, HH:mm")}
            </div>
          </div>
          <div className="grid grid-cols-3 gap-2">
            <div className="font-semibold dark:text-white">Action</div>
            <div className="col-span-2 dark:text-slate-400">
              {entry.message}
            </div>
          </div>
        </div>
      ))}
    </>
  );
}
