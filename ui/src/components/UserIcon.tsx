import { IPublicClientApplication } from "@azure/msal-browser";
import { useMsal } from "@azure/msal-react";
import { Link } from "@tanstack/react-router";
import { useEffect, useRef, useState } from "react";
import { FaSignOutAlt } from "react-icons/fa";
import { HiCog } from "react-icons/hi";

import { randomColor } from "../utils/assets";

function signOutClickHandler(
  instance: IPublicClientApplication,
  homeAccountId: string,
) {
  const logoutRequest = {
    account: instance.getAccountByHomeId(homeAccountId),
    postLogoutRedirectUri: import.meta.env.VITE_POST_LOGOUT_REDIRECT_URI,
  };
  instance.logoutRedirect(logoutRequest);
}

export default function UserIcon() {
  const { instance, accounts } = useMsal();
  const dropdownRef = useRef<HTMLDivElement | null>(null);
  const [bgColor, setBgColor] = useState("");
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  const toggleDropdown = () => {
    setIsDropdownOpen(!isDropdownOpen);
  };

  const handleClickOutside = (event: MouseEvent) => {
    if (
      dropdownRef.current &&
      !dropdownRef.current.contains(event.target as Node)
    ) {
      setIsDropdownOpen(false);
    }
  };

  useEffect(() => {
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  useEffect(() => {
    const storedColor = localStorage.getItem("profileIconColor");
    if (storedColor) {
      setBgColor(storedColor);
    } else {
      const newColor = randomColor();
      setBgColor(newColor);
      localStorage.setItem("profileIconColor", newColor);
    }
  }, []);

  return (
    <>
      <div
        className={`${bgColor} mr-3 h-8 w-8 cursor-pointer content-center rounded-full text-center text-xs text-white lg:h-11 lg:w-11 lg:text-base`}
        onClick={toggleDropdown}
      >
        {accounts[0]?.name &&
          accounts[0]?.name
            .split(" ")
            .map((name) => name[0])
            .join("")
            .toUpperCase()}
      </div>
      {isDropdownOpen && (
        <div
          ref={dropdownRef}
          className="absolute top-full right-0 mt-2 rounded-sm border-2 border-slate-200 bg-white shadow-lg dark:border-slate-700 dark:bg-slate-800"
        >
          <div className="flex flex-col items-start p-4">
            <div className="text-medium mb-1 flex items-center justify-center rounded-full px-1 font-bold text-black dark:text-white">
              {accounts[0]?.name}
            </div>
            <div className="mb-2 flex items-center justify-center rounded-full px-1 text-sm text-black dark:text-slate-300">
              {accounts[0]?.username}
            </div>
            <Link
              to="/users/$userId"
              params={{
                userId: accounts[0]?.localAccountId,
              }}
              className="w-full rounded-sm hover:bg-slate-100 dark:hover:bg-slate-600"
            >
              <div className="flex space-x-3 rounded-full p-2 text-black dark:text-slate-300">
                <HiCog className="h-6 w-6 opacity-70 dark:text-white" />
                <div>Settings</div>
              </div>
            </Link>
            <div className="flex w-full cursor-pointer flex-row items-center rounded-sm p-2 hover:bg-slate-100 dark:hover:bg-slate-600">
              <FaSignOutAlt className="h-6 w-6 cursor-pointer opacity-70 dark:text-white" />
              <button
                className="ml-2 min-w-0 cursor-pointer p-1 pb-0 text-left whitespace-nowrap text-black dark:text-white"
                onClick={() =>
                  signOutClickHandler(instance, accounts[0].homeAccountId)
                }
              >
                Sign out
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
