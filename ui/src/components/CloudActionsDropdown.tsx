import { <PERSON>u, MenuButton, MenuItem, MenuItems } from "@headlessui/react";
import { useQueryClient } from "@tanstack/react-query";
import { useMemo, useState } from "react";
import { HiChevronDown } from "react-icons/hi";
import { toast } from "react-toastify";
import {
  getGetEngagementCloudInstancesQueryKey,
  // AWS actions
  usePostNodesCloudInstanceReboot,
  usePostNodesCloudInstanceStop,
  usePostNodesCloudInstanceTerminate,
  usePostNodesCloudInstanceStart,
  usePostNodesCloudInstanceReip,
  // Azure actions
  usePostAzureNodesCloudInstanceStart,
  usePostAzureNodesCloudInstanceStop,
  usePostAzureNodesCloudInstanceRestart,
  usePostAzureNodesCloudInstanceReip,
  usePostAzureNodesCloudInstanceTerminate,
} from "../client";
import ConfirmationModal from "./ConfirmationModal";

export default function CloudActionsDropdown({
  engagementId,
  selectedNodeIds,
  selectedStates,
  onActionComplete,
}: {
  engagementId: string;
  selectedNodeIds: string[];
  selectedStates?: string[]; // optional: states of selected instances
  onActionComplete?: () => void; // callback to clear selections after action
}) {
  const queryClient = useQueryClient();
  const [isConfirmOpen, setIsConfirmOpen] = useState(false);
  const [isReipConfirmOpen, setIsReipConfirmOpen] = useState(false);


  // AWS
  const { mutateAsync: stopAwsAsync, isPending: isStoppingAws } =
    usePostNodesCloudInstanceStop();
  const { mutateAsync: rebootAwsAsync, isPending: isRebootingAws } =
    usePostNodesCloudInstanceReboot();
  const { mutateAsync: terminateAwsAsync, isPending: isTerminatingAws } =
    usePostNodesCloudInstanceTerminate();
  const { mutateAsync: startAwsAsync, isPending: isStartingAws } =
    usePostNodesCloudInstanceStart();
  const { mutateAsync: reipAwsAsync, isPending: isReipAws } =
    usePostNodesCloudInstanceReip();
  // Azure
  const { mutateAsync: startAzureAsync, isPending: isStartingAzure } =
    usePostAzureNodesCloudInstanceStart();
  const { mutateAsync: stopAzureAsync, isPending: isStoppingAzure } =
    usePostAzureNodesCloudInstanceStop();
  const { mutateAsync: restartAzureAsync, isPending: isRestartingAzure } =
    usePostAzureNodesCloudInstanceRestart();
  const { mutateAsync: reipAzureAsync, isPending: isReipAzure } =
    usePostAzureNodesCloudInstanceReip();
  const { mutateAsync: terminateAzureAsync, isPending: isTerminatingAzure } =
    usePostAzureNodesCloudInstanceTerminate();

  const disabledGlobal =
    selectedNodeIds.length === 0 ||
    isStoppingAws || isRebootingAws || isTerminatingAws || isStartingAws || isReipAws ||
    isStartingAzure || isStoppingAzure || isRestartingAzure || isReipAzure || isTerminatingAzure;

  // Disable based on states and providers
  const { canStop, canReboot, canTerminate, canStart, canReip } = useMemo(() => {


    if (!selectedStates || selectedStates.length === 0) {
      return {
        canStop: !disabledGlobal,
        canReboot: !disabledGlobal,
        canTerminate: !disabledGlobal,
        canStart: !disabledGlobal,
        canReip: !disabledGlobal,
      };
    }
    const lc = selectedStates.map((s) => (s || "").toLowerCase());
    const allRunningOrPending = lc.every((s) => ["running", "pending"].includes(s));
    const noneTerminated = lc.every((s) => s !== "terminated" && s !== "shutting-down");
    return {
      canStop: !disabledGlobal && lc.some((s) => s === "running"),
      canReboot: !disabledGlobal && allRunningOrPending,
      canTerminate: !disabledGlobal && noneTerminated,
      canStart: !disabledGlobal && lc.every((s) => s === "stopped"),
      canReip: !disabledGlobal && lc.every((s) => ["running", "stopped"].includes(s)),
    };
  }, [selectedStates, selectedNodeIds, engagementId, disabledGlobal, queryClient]);

  const optimisticStateFor = (
    act: "stop" | "reboot" | "terminate" | "start" | "reip",
  ) => {
    switch (act) {
      case "stop":
        return "stopping";
      case "start":
        return "pending";
      case "reboot":
        return "pending";
      case "terminate":
        return "shutting-down";
      case "reip":
        return "stopping";
      default:
        return undefined;
    }
  };

  const doAction = async (
    action: "stop" | "reboot" | "terminate" | "start" | "reip",
  ) => {
    if (selectedNodeIds.length === 0) return;
    const actionTitle = action.charAt(0).toUpperCase() + action.slice(1);
    const toastId = toast.loading(`${actionTitle} in progress for ${selectedNodeIds.length} instance(s)...`);

    // Helper: get provider for a node id from cached engagement instances
    const getProviderForNode = (nodeId: string): string | undefined => {
      const key = getGetEngagementCloudInstancesQueryKey(engagementId);
      const cached: any = queryClient.getQueryData(key);
      const groups = cached?.cloud_instance_node_groups || [];
      for (const ng of groups) {
        const arr = ng?.cloud_instances || [];
        const found = arr.find((ci: any) => ci?.id === nodeId);
        if (found) return (found.provider || "").toUpperCase();
      }
      return undefined;
    };

    try {
      const requests = selectedNodeIds.map((nodeID) => {
        const provider = getProviderForNode(nodeID);
        if (provider === "AZURE") {
          if (action === "stop") return stopAzureAsync({ nodeID });
          if (action === "reboot") return restartAzureAsync({ nodeID });
          if (action === "start") return startAzureAsync({ nodeID });
          if (action === "reip") return reipAzureAsync({ nodeID });
          if (action === "terminate") return terminateAzureAsync({ nodeID });
          return Promise.resolve();
        }
        // default to AWS
        if (action === "stop") return stopAwsAsync({ nodeID });
        if (action === "reboot") return rebootAwsAsync({ nodeID });
        if (action === "terminate") return terminateAwsAsync({ nodeID });
        if (action === "start") return startAwsAsync({ nodeID });
        if (action === "reip") return reipAwsAsync({ nodeID });
        return Promise.resolve();
      });
      await Promise.allSettled(requests);

      // Optimistically set transient state for selected rows to kick off polling UI
      const key = getGetEngagementCloudInstancesQueryKey(engagementId);
      const optimistic = optimisticStateFor(action);
      if (optimistic) {
        queryClient.setQueryData(key, (prev: any) => {
          if (!prev) return prev;
          const groups = [...(prev.cloud_instance_node_groups || [])];
          for (const ng of groups) {
            if (!ng?.cloud_instances) continue;
            for (const ci of ng.cloud_instances) {
              if (selectedNodeIds.includes(ci?.id)) {
                ci.cloud_instance_state = optimistic;
              }
            }
          }
          return { ...prev, cloud_instance_node_groups: groups };
        });
      }

      // Single immediate invalidate; ongoing polling is controlled by refetchInterval in the page
      await queryClient.invalidateQueries({ queryKey: key });

      toast.update(toastId, {
        render: `${actionTitle} requested for ${selectedNodeIds.length} instance(s)`,
        type: "success",
        isLoading: false,
        autoClose: 2000,
      });

      // Clear selections after successful action
      onActionComplete?.();
    } catch (e) {
      toast.update(toastId, {
        render: `Failed to ${action} instance(s)`,
        type: "error",
        isLoading: false,
        autoClose: 4000,
      });
    }
  };

  return (
    <>
      <Menu as="div" className="relative inline-block text-left ml-2">
        <div>
          <MenuButton
            disabled={disabledGlobal}
            className={`inline-flex justify-center rounded-sm border border-gray-300 px-3 py-1 text-sm ${disabledGlobal ? "opacity-50 cursor-not-allowed" : "cursor-pointer hover:bg-slate-100"}`}
          >
            Actions
            <HiChevronDown className="ml-2 h-4 w-4" />
          </MenuButton>
        </div>
        <MenuItems className="absolute right-0 mt-2 w-48 origin-top-right divide-y divide-gray-100 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none z-50">
          <div className="py-1">
            <MenuItem>
              {({ active }) => (
                <button
                  className={`${active ? "bg-gray-100" : ""} block w-full px-4 py-2 text-left text-sm ${!canStop ? "opacity-50 cursor-not-allowed" : ""}`}
                  onClick={() => canStop && doAction("stop")}
                  disabled={!canStop}
                >
                  Stop
                </button>
              )}
            </MenuItem>
            <MenuItem>
              {({ active }) => (
                <button
                  className={`${active ? "bg-gray-100" : ""} block w-full px-4 py-2 text-left text-sm ${!canReboot ? "opacity-50 cursor-not-allowed" : ""}`}
                  onClick={() => canReboot && doAction("reboot")}
                  disabled={!canReboot}
                  title="For Azure, Reboot performs Restart"
                >
                  Reboot
                </button>
              )}
            </MenuItem>
            <MenuItem>
              {({ active }) => (
                <button
                  className={`${active ? "bg-gray-100" : ""} block w-full px-4 py-2 text-left text-sm ${!canStart ? "opacity-50 cursor-not-allowed" : ""}`}
                  onClick={() => canStart && doAction("start")}
                  disabled={!canStart}
                >
                  Start
                </button>
              )}
            </MenuItem>
            <MenuItem>
              {({ active }) => (
                <button
                  className={`${active ? "bg-gray-100" : ""} block w-full px-4 py-2 text-left text-sm ${!canReip ? "opacity-50 cursor-not-allowed" : ""}`}
                  onClick={() => canReip && setIsReipConfirmOpen(true)}
                  disabled={!canReip}
                >
                  ReIP (Stop + Start)
                </button>
              )}
            </MenuItem>
            <MenuItem>
              {({ active }) => (
                <button
                  className={`${active ? "bg-gray-100" : ""} block w-full px-4 py-2 text-left text-sm text-red-600 ${!canTerminate ? "opacity-50 cursor-not-allowed" : ""}`}
                  onClick={() => canTerminate && setIsConfirmOpen(true)}
                  disabled={!canTerminate}
                >
                  Terminate
                </button>
              )}
            </MenuItem>
          </div>
        </MenuItems>
      </Menu>

      <ConfirmationModal
        isOpen={isConfirmOpen}
        closeModal={() => setIsConfirmOpen(false)}
        title="Terminate instances?"
        message="This will request termination for the selected instance(s). This action cannot be undone."
        confirmButtonText="Terminate"
        confirmButtonVariant="danger"
        onConfirm={() => doAction("terminate")}
      />
      <ConfirmationModal
        isOpen={isReipConfirmOpen}
        closeModal={() => setIsReipConfirmOpen(false)}
        title="ReIP instances (Stop + Start)?"
        message="This will stop and then start the selected instance(s) and attempt to refresh their public IP."
        confirmButtonText="ReIP"
        confirmButtonVariant="primary"
        onConfirm={() => doAction("reip")}
      />
    </>
  );
}

