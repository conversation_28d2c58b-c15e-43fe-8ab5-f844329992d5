import { DialogTitle } from "@headlessui/react";
import { useQueryClient } from "@tanstack/react-query";
import { useState } from "react";
import { toast } from "react-toastify";

import { getGetUserDetailsQueryKey, useDeleteUserSshKey } from "../../client";
import { ActionButtons, ButtonProps } from "../ActionButtons";
import ErrorMessageModal, { ErrorMessage } from "../ErrorMessageModal";
import Modal from "../Modal";

type DeleteSSHKeyModalProps = {
  isOpen: boolean;
  closeModal: () => void;
  userId: string;
};

export default function DeleteSSHKeyModal({
  isOpen,
  closeModal,
  userId,
}: DeleteSSHKeyModalProps) {
  const queryClient = useQueryClient();
  const [isOpenErrorModal, setIsOpenErrorModal] = useState<boolean>(false);
  const [errorMessage, setErrorMessage] = useState<ErrorMessage | null>(null);

  const deleteKeyMutation = useDeleteUserSshKey({
    mutation: {
      onError: () => {
        openErrorModal({
          title: "Error deleting SSH Key.",
          message: "Please try again later.",
        });
      },
      onSettled: (_data: any, error: unknown) => {
        if (!error) {
          toast.success("SSH Key has been successfully deleted.");
        }
        const queryKey = getGetUserDetailsQueryKey(userId);
        queryClient.invalidateQueries({ queryKey });
        closeModal();
      },
    },
  });

  const primaryButton: ButtonProps = {
    label: "Delete",
    onClick: () => {
      deleteKeyMutation.mutate({ userId: userId });
    },
    variant: "primary",
  };

  const secondaryButton: ButtonProps = {
    label: "Cancel",
    onClick: () => {
      closeModal();
    },
    variant: "secondary",
  };

  function openErrorModal(errorMessage: ErrorMessage) {
    if (errorMessage) {
      setErrorMessage(errorMessage);
    }
    setIsOpenErrorModal(true);
  }

  function closeErrorModal() {
    setIsOpenErrorModal(false);
  }

  return (
    <>
      <Modal
        title={``}
        isOpen={isOpen}
        closeModal={closeModal}
        widthClass="w-11/12 sm:w-10/12 md:w-5/12 lg:w-4/12"
      >
        <div className="flex flex-row justify-between">
          <DialogTitle
            as="h3"
            className="flex flex-row items-center justify-between space-x-12 pb-4 text-xl font-semibold text-black dark:text-slate-100"
          >
            <div>Confirm the deletion of SSH Key</div>
          </DialogTitle>
        </div>
        <div className="mt-4 flex justify-end space-x-2">
          <ActionButtons
            primaryButton={primaryButton}
            secondaryButton={secondaryButton}
          />
        </div>
      </Modal>
      <ErrorMessageModal
        isOpen={isOpenErrorModal}
        closeModal={closeErrorModal}
        errorMessage={errorMessage}
      />
    </>
  );
}
