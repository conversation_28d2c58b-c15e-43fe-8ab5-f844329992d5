import { useState } from "react";

import { SetSshKeyInputBody } from "../../model";
import { ActionButtons, ButtonProps } from "../ActionButtons";

interface SSHKeyFormProps {
  setShowSSHForm: (showSSHForm: boolean) => void;
  handleSetSSHKey: (data: SetSshKeyInputBody) => void;
}

export default function AddSshKeyForm({
  setShowSSHForm,
  handleSetSSHKey,
}: SSHKeyFormProps) {
  const [sshData, setSshData] = useState({
    ssh_key_label: "",
    ssh_key: "",
  });
  const primaryAddSSHButton: ButtonProps = {
    label: "Add Key",
    onClick: () => {
      handleSetSSHKey(sshData);
      setShowSSHForm(false);
      setSshData({
        ssh_key_label: "",
        ssh_key: "",
      });
    },
    variant: "primary",
    disabled: !(sshData.ssh_key_label !== "" && sshData.ssh_key !== ""),
  };

  const secondaryAddSSHButton: ButtonProps = {
    label: "Cancel",
    onClick: () => {
      setShowSSHForm(false);
      setSshData({
        ssh_key_label: "",
        ssh_key: "",
      });
    },
    variant: "secondary",
  };

  const handleSSHChange = (e: { target: { name: string; value: string } }) => {
    const { name, value } = e.target;
    setSshData({
      ...sshData,
      [name]: value,
    });
  };

  return (
    <>
      <hr className="my-8 h-px border-0 bg-gray-200 dark:bg-gray-700" />
      <div className="mb-4 text-xl font-semibold text-black dark:text-white">
        Add an SSH Key
      </div>
      <div className="mb-8 flex w-full flex-col space-y-4 md:flex-row md:space-y-0">
        <div className="flex w-full flex-col md:w-1/2">
          <label className="mb-1 flex flex-row space-x-1 dark:text-white">
            <span className="font-semibold">Label</span>
            <span className="text-red-600">*</span>
          </label>
          <input
            className="w-full rounded-sm border border-solid border-gray-400 px-4 py-2 text-gray-700 focus:ring-2 focus:ring-purple-700 focus:outline-hidden md:w-5/6 dark:border-transparent dark:bg-slate-600 dark:text-slate-100"
            type="text"
            name="ssh_key_label"
            value={sshData.ssh_key_label}
            onChange={handleSSHChange}
          />
        </div>
        <div className="flex w-full flex-col md:w-1/2">
          <label className="mb-1 flex flex-row space-x-1 dark:text-white">
            <span className="font-semibold">Key</span>
            <span className="text-red-600">*</span>
          </label>
          <input
            className="w-full rounded-sm border border-solid border-gray-400 px-4 py-2 text-gray-700 focus:ring-2 focus:ring-purple-700 focus:outline-hidden md:w-5/6 dark:border-transparent dark:bg-slate-600 dark:text-slate-100"
            type="text"
            name="ssh_key"
            value={sshData.ssh_key}
            onChange={handleSSHChange}
          />
        </div>
      </div>
      <ActionButtons
        primaryButton={primaryAddSSHButton}
        secondaryButton={secondaryAddSSHButton}
      />
    </>
  );
}
