import { <PERSON>u, <PERSON>uButton, <PERSON>uItem, MenuItems } from "@headlessui/react";
import { useState } from "react";
import { BsChevronDown, BsCloudPlus } from "react-icons/bs";
import { FaAws } from "react-icons/fa";
import { VscAzure } from "react-icons/vsc";

import AWSAccountFlow from "./AWSAccountFlow";
import AzureTenantFlow from "./AzureTenantFlow";

type Props = {
  engagementID: string;
};

export default function CreateAccountsButton({ engagementID }: Props) {
  const [showAWSModal, setShowAWSModal] = useState(false);
  const [showAzureModal, setShowAzureModal] = useState(false);

  const closeAWSModal = () => setShowAWSModal(false);
  const closeAzureModal = () => setShowAzureModal(false);

  return (
    <>
      <Menu as="div" className="relative inline-block text-left">
        <MenuButton className="flex items-center space-x-2 rounded-md bg-indigo-700 px-4 py-2 text-sm font-medium text-white transition-colors hover:bg-indigo-800">
          <BsCloudPlus className="h-4 w-4" />
          <span>Cloud Accounts</span>
          <BsChevronDown className="h-3 w-3" />
        </MenuButton>

        <MenuItems className="absolute right-0 z-10 mt-2 w-56 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black/10 focus:outline-none dark:bg-gray-800">
          <div className="p-1">
            <MenuItem>
              {({ active }) => (
                <button
                  onClick={() => setShowAWSModal(true)}
                  className={`${
                    active
                      ? "bg-purple-600 text-white"
                      : "text-gray-900 dark:text-white"
                  } group flex w-full items-center rounded-md px-4 py-2 text-sm`}
                >
                  <FaAws className="mr-2 h-4 w-4" />
                  AWS Account
                </button>
              )}
            </MenuItem>
            <MenuItem>
              {({ active }) => (
                <button
                  onClick={() => setShowAzureModal(true)}
                  className={`${
                    active
                      ? "bg-indigo-600 text-white"
                      : "text-gray-900 dark:text-white"
                  } group flex w-full items-center rounded-md px-4 py-2 text-sm`}
                >
                  <VscAzure className="mr-2 h-4 w-4" />
                  Azure Tenant
                </button>
              )}
            </MenuItem>
          </div>
        </MenuItems>
      </Menu>

      {/* AWS Account Flow Modal */}
      {showAWSModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center overflow-y-auto bg-black/50">
          <div className="mx-4 my-4 flex max-h-[90vh] w-full max-w-sm flex-col rounded-lg bg-white p-4 shadow-xl sm:max-w-md sm:p-6 md:max-w-2xl lg:max-w-4xl xl:max-w-5xl dark:bg-gray-800">
            <div className="mb-4 flex items-center justify-between">
              <h2 className="text-xl font-bold dark:text-white">
                <FaAws className="mr-2 inline-block h-5 w-5" />
                AWS Account Management
              </h2>
              <button
                onClick={closeAWSModal}
                className="text-gray-500 hover:text-gray-700 dark:text-gray-300 dark:hover:text-white"
              >
                ✕
              </button>
            </div>
            <div className="flex-1 overflow-y-auto">
              <AWSAccountFlow
                engagementID={engagementID}
                closeModal={closeAWSModal}
              />
            </div>
          </div>
        </div>
      )}

      {/* Azure Tenant Flow Modal */}
      {showAzureModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center overflow-y-auto bg-black/50">
          <div className="mx-4 my-4 flex max-h-[90vh] w-full max-w-sm flex-col rounded-lg bg-white p-4 shadow-xl sm:max-w-md sm:p-6 md:max-w-2xl lg:max-w-4xl xl:max-w-5xl dark:bg-gray-800">
            <div className="mb-4 flex items-center justify-between">
              <h2 className="text-xl font-bold dark:text-white">
                <VscAzure className="mr-2 inline-block h-5 w-5" />
                Azure Tenant Management
              </h2>
              <button
                onClick={closeAzureModal}
                className="text-gray-500 hover:text-gray-700 dark:text-gray-300 dark:hover:text-white"
              >
                ✕
              </button>
            </div>
            <AzureTenantFlow
              engagementID={engagementID}
              closeModal={closeAzureModal}
            />
          </div>
        </div>
      )}
    </>
  );
}
