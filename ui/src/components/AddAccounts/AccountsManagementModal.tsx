import {
  Dialog,
  DialogPanel,
  DialogTitle,
  Transition,
  TransitionChild,
} from "@headlessui/react";
import { Fragment, useState } from "react";
import { HiChevronLeft } from "react-icons/hi";

import AWSAccountFlow from "./AWSAccountFlow";
import AzureTenantFlow from "./AzureTenantFlow";

type AddAccountScreen =
  | "SelectAccountType"
  | "AddAWSAccountDetails"
  | "AddAzureTenantDetails";

type Props = {
  engagementID: string;
  isOpen: boolean;
  closeModal: () => void;
};

export default function AddAccountsModal({
  engagementID,
  isOpen,
  closeModal,
}: Props) {
  const [screen, setScreen] = useState<AddAccountScreen>("SelectAccountType");

  const resetModal = () => {
    setScreen("SelectAccountType");
    closeModal();
  };

  const getTitle = () => {
    switch (screen) {
      case "AddAWSAccountDetails":
        return "AWS Account Details";
      case "AddAzureTenantDetails":
        return "Azure Tenants Details";
      default:
        return "";
    }
  };

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-30" onClose={resetModal}>
        <TransitionChild
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black/25" />
        </TransitionChild>

        <div className="fixed inset-0 overflow-y-auto dark:text-white">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <TransitionChild
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <DialogPanel className="w-11/12 transform overflow-hidden rounded-2xl bg-white p-8 text-left align-middle shadow-xl transition-all sm:w-10/12 md:w-8/12 lg:p-10 dark:bg-[#364151]">
                <DialogTitle
                  as="h3"
                  className="flex flex-row items-center space-x-4 text-xl font-bold text-black md:space-x-0 lg:text-3xl dark:text-white"
                >
                  {screen !== "SelectAccountType" && (
                    <button
                      className="flex flex-row text-gray-600 hover:text-gray-800 md:flex md:hidden"
                      onClick={() => setScreen("SelectAccountType")}
                    >
                      <HiChevronLeft className="h-4 w-4 dark:text-white" />
                    </button>
                  )}
                  <div>{screen === "SelectAccountType" ? "" : getTitle()}</div>
                </DialogTitle>

                <div id="dialog-content" className="space-y-8">
                  {screen === "SelectAccountType" && (
                    <div className="flex flex-col space-y-4 pt-4">
                      <button
                        onClick={() => setScreen("AddAWSAccountDetails")}
                        className="w-full rounded-md bg-purple-700 px-4 py-3 text-white hover:bg-purple-800"
                      >
                        AWS Accounts
                      </button>
                      <button
                        onClick={() => setScreen("AddAzureTenantDetails")}
                        className="w-full rounded-md bg-indigo-600 px-4 py-3 text-white hover:bg-indigo-700"
                      >
                        Azure Tenants
                      </button>
                    </div>
                  )}

                  {screen === "AddAWSAccountDetails" && (
                    <AWSAccountFlow
                      engagementID={engagementID}
                      closeModal={resetModal}
                    />
                  )}

                  {screen === "AddAzureTenantDetails" && (
                    <AzureTenantFlow
                      engagementID={engagementID}
                      closeModal={resetModal} // Close modal function for Azure
                    />
                  )}
                </div>

                <div
                  id="node-selection-actions"
                  className="flex flex-row items-center justify-end space-x-4 pt-4"
                ></div>
              </DialogPanel>
            </TransitionChild>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
}
