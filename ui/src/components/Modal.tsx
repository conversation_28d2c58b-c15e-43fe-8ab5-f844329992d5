import {
  Dialog,
  DialogPanel,
  DialogTitle,
  Transition,
  TransitionChild,
} from "@headlessui/react";
import { Fragment, ReactNode } from "react";

import { useTheme } from "../context/ThemeProvider";

type ModalProps = {
  title: string;
  isOpen: boolean;
  closeModal: () => void;
  children: ReactNode;
  widthClass?: string;
};

export default function Modal({
  title,
  isOpen,
  closeModal,
  children,
  widthClass = "w-11/12 sm:w-10/12 md:w-8/12",
}: ModalProps) {
  const { isDarkMode } = useTheme();
  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-30" onClose={closeModal}>
        <TransitionChild
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black/25" />
        </TransitionChild>
        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <TransitionChild
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <DialogPanel
                className={`${widthClass} transform rounded-2xl bg-white p-10 text-left align-middle shadow-xl transition-all dark:bg-[#364151]`}
                style={{ overflow: "visible" }}
              >
                <DialogTitle
                  as="h3"
                  className="flex flex-row items-center justify-between text-xl font-bold text-black md:space-x-12 dark:text-white"
                >
                  <div>{title}</div>
                  <button
                    className="cursor-pointer text-gray-500 hover:text-gray-700 focus:outline-hidden dark:text-white"
                    aria-label="Close"
                    onClick={closeModal}
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke={isDarkMode ? "white" : "currentColor"}
                      className="h-6 w-6"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M6 18L18 6M6 6l12 12"
                      />
                    </svg>
                  </button>
                </DialogTitle>
                <div
                  id="dialog-content"
                  className="space-y-8"
                  style={{ position: "relative" }}
                >
                  {children}
                </div>
              </DialogPanel>
            </TransitionChild>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
}
