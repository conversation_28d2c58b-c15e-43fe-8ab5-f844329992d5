import { useSuspenseQuery } from "@tanstack/react-query";
import { useMemo, useState } from "react";
import { FaFolder } from "react-icons/fa";
import { HiMagnifyingGlass } from "react-icons/hi2";

import { getEngagements, getGetEngagementsQueryKey } from "../client";
import { useTheme } from "../context/ThemeProvider";
import { Engagement } from "../model";
import BreadCrumbs from "./BreadCrumbs";
import EngagementItem from "./EngagementItem";
import Header from "./Header";
import ResponsiveSideNav from "./ResponsiveSideNav";

function ArchiveManagement() {
  const { isDarkMode } = useTheme();
  const [isSideNavOpen, setIsSideNavOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const toggleSideNav = () => setIsSideNavOpen((prev) => !prev);

  const engagementsQueryKey = getGetEngagementsQueryKey();
  const engagementsQueryFn = () => getEngagements();
  const { data: engagementList } = useSuspenseQuery({
    queryKey: engagementsQueryKey,
    queryFn: engagementsQueryFn,
  });

  const allEngagements = engagementList.engagements || [];

  const archivedEngagements = allEngagements
    .filter((engagement) => engagement.is_active === false)
    .sort(
      (a, b) =>
        new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime(),
    );

  const filteredEngagements = useMemo(() => {
    if (searchQuery.length < 3) return archivedEngagements;

    const query = searchQuery.toLowerCase();
    return archivedEngagements.filter(
      (engagement: Engagement) =>
        engagement.title.toLowerCase().includes(query) ||
        engagement.client_name.toLowerCase().includes(query),
    );
  }, [archivedEngagements, searchQuery]);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  return (
    <div className="flex h-full w-full">
      <ResponsiveSideNav
        toggleSideNav={toggleSideNav}
        isSideNavOpen={isSideNavOpen}
      />
      <div className="flex w-full flex-col">
        <Header toggleSideNav={toggleSideNav} />
        <div className="flex h-full flex-col bg-slate-50 p-6 dark:bg-slate-800">
          <BreadCrumbs />
          <div
            id="archive-page-main-title"
            className="flex flex-col space-y-2 py-4"
          >
            <span className="text-3xl font-semibold text-black dark:text-white">
              Archive Management
            </span>
          </div>
          <div className="flex w-full flex-col justify-between py-3 md:flex-row md:items-center md:space-y-0 md:space-x-3 dark:text-white">
            <div className="order-last flex flex-row items-center rounded-sm border border-solid border-gray-200 bg-white px-4 focus-within:ring-2 focus-within:ring-purple-500 focus-within:outline-hidden md:order-none dark:bg-slate-700">
              <HiMagnifyingGlass
                className={`${isDarkMode ? "text-white" : "text-black"} h-5 w-5`}
              />
              <input
                id="search-archive-input"
                className="w-80 px-2 py-2 focus:outline-hidden dark:border-slate-50 dark:bg-slate-700"
                type="text"
                value={searchQuery}
                onChange={handleSearchChange}
                placeholder="Search engagements or title (min 3 char.)"
              />
            </div>
          </div>

          <div>
            {filteredEngagements.length === 0 ? (
              <div className="rounded-lg bg-gray-50 py-12 text-center dark:bg-slate-700">
                <FaFolder className="mx-auto mb-4 text-4xl text-gray-400" />
                <h3 className="mb-2 text-lg font-medium text-gray-900 dark:text-white">
                  {searchQuery.length >= 3
                    ? "No archived engagements found"
                    : "No archived engagements"}
                </h3>
                <p className="text-gray-500 dark:text-gray-400">
                  {searchQuery.length >= 3
                    ? "Try adjusting your search terms."
                    : "Archived engagements will appear here when you archive them from the main engagements page."}
                </p>
              </div>
            ) : (
              <div className="flex flex-col space-y-3">
                {filteredEngagements.map((engagement: Engagement) => (
                  <EngagementItem
                    key={engagement.id}
                    id={engagement.id}
                    clientName={engagement.client_name}
                    title={engagement.title}
                    users={engagement.users}
                    status={engagement.status}
                    engagement={engagement}
                    isArchivePage={true}
                  />
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export default ArchiveManagement;
