import React from "react";

const DomainStatusLegend: React.FC = () => {
  const statuses = [
    { name: "Assigned", class: "bg-green-100 text-green-800" },
    { name: "Unassigned", class: "bg-gray-100 text-gray-800" },
    { name: "Burned", class: "bg-red-100 text-red-800" },
    { name: "Quarantine", class: "bg-yellow-100 text-yellow-800" },
    { name: "Expired", class: "bg-blue-100 text-blue-800" },
  ];

  return (
    <div className="mb-4 flex flex-wrap items-center gap-3 text-xs">
      <span className="font-medium">Status Legend:</span>
      {statuses.map((status) => (
        <div key={status.name} className="flex items-center">
          <span
            className={`inline-block rounded-full px-2 py-1 text-xs font-medium ${status.class} mr-1`}
          >
            {status.name}
          </span>
        </div>
      ))}
    </div>
  );
};

export default DomainStatusLegend;
