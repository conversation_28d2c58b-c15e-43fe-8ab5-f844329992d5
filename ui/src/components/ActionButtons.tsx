export interface ButtonProps {
  icon?: string;
  label: string;
  onClick: () => void;
  variant: string;
  disabled?: boolean;
}

export interface EventButtonProps {
  icon?: string;
  label: string;
  onClick: (e: React.MouseEvent<HTMLButtonElement>) => void;
  variant: string;
  disabled?: boolean;
}

export interface ActionButtonProps {
  primaryButton?: ButtonProps | EventButtonProps;
  secondaryButton?: ButtonProps;
}

export const ActionButtons = ({
  primaryButton,
  secondaryButton,
}: ActionButtonProps) => {
  return (
    <div className="flex w-full flex-col justify-end md:w-max md:flex-row md:space-x-4">
      {secondaryButton && (
        <button
          className={`rounded-md border border-solid border-slate-300 px-6 py-3 ${secondaryButton.disabled ? "bg-gray-100 text-slate-700 opacity-60 dark:border-slate-600 dark:text-slate-400" : "order-last cursor-pointer hover:bg-slate-100 md:order-none dark:text-white dark:hover:bg-slate-600"}`}
          onClick={secondaryButton.onClick}
          disabled={secondaryButton.disabled}
        >
          <span>{secondaryButton.label}</span>
        </button>
      )}
      {primaryButton && (
        <button
          type="button"
          onClick={primaryButton.onClick}
          className={`order-first mb-2 space-x-3 rounded-md md:order-none md:mb-0 ${primaryButton.disabled ? "bg-gray-400" : "cursor-pointer bg-purple-700 hover:bg-purple-800"} px-6 py-3 text-white`}
          disabled={primaryButton.disabled}
        >
          {primaryButton.icon && <span>{primaryButton.icon}</span>}
          <span>{primaryButton.label}</span>
        </button>
      )}
    </div>
  );
};
