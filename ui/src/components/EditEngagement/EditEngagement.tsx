import { useQueryClient } from "@tanstack/react-query";
import { MultiSelectChangeEvent } from "primereact/multiselect";
import { ChangeEvent, useEffect, useState } from "react";
import { HiPencil } from "react-icons/hi";
import { toast } from "react-toastify";

import {
  getGetEngagementQueryKey,
  getGetUsersQueryKey,
  useEditEngagement,
  useGetUsers,
} from "../../client.ts";
import { useTheme } from "../../context/ThemeProvider";
import { Engagement, EngagementUser } from "../../model";
import ErrorMessageModal, { ErrorMessage } from "../ErrorMessageModal";
import EditEngagementModal from "./EditEngagementModal";

type Props = {
  engagementID: string;
  engagement: Engagement;
};

export default function EditEngagement({ engagementID, engagement }: Props) {
  const queryClient = useQueryClient();
  const [formData, setFormData] = useState({
    client_name: "",
    wbs_code: "",
    title: "",
    users: [] as EngagementUser[],
  });
  const { isDarkMode } = useTheme();
  const [isOpenErrorModal, setIsOpenErrorModal] = useState<boolean>(false);
  const [errorMessage, setErrorMessage] = useState<ErrorMessage | null>(null);
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    if (engagement) {
      const { client_name, wbs_code, title, users } = engagement;
      setFormData({
        client_name: client_name || "",
        wbs_code: wbs_code || "",
        title: title || "",
        users: users || [],
      });
    }
  }, [engagement]);

  const { data: availableUsers } = useGetUsers({
    query: {
      queryKey: getGetUsersQueryKey(),
      enabled: isOpen,
    },
  });

  const users = availableUsers?.users || [];

  const handleMultiSelectChange = (e: MultiSelectChangeEvent) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });
  };

  const handleInputChange = (e: ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });
  };

  const updateEngagementMutation = useEditEngagement({
    mutation: {
      onError: () => {
        openErrorModal({
          title: "Error updating engagement",
          message: "Please try again later.",
        });
      },
      onSettled: (_data: any, error: unknown) => {
        if (!error) {
          toast.success("Engagement has been successfully updated.");
        }
        const queryKey = getGetEngagementQueryKey(engagementID);
        queryClient.invalidateQueries({ queryKey });
        closeModal();
      },
    },
  });

  function handleSubmit(
    engagementID: string,
    title: string,
    wbsCode: string,
    users: EngagementUser[],
  ) {
    updateEngagementMutation.mutate({
      engagementID: engagementID,
      data: {
        wbs_code: wbsCode,
        title: title,
        user_ids: users.map((user) => user.id),
      },
    });
  }

  function closeModal() {
    setIsOpen(false);
  }

  function openModal() {
    setIsOpen(true);
  }

  function openErrorModal(errorMessage: ErrorMessage) {
    if (errorMessage) {
      setErrorMessage(errorMessage);
    }
    setIsOpenErrorModal(true);
  }

  function closeErrorModal() {
    setIsOpenErrorModal(false);
  }

  useEffect(() => {
    const availableUsersList = availableUsers?.users ?? [];
    if (engagement && engagement.users) {
      const matchingUsers = engagement.users
        .map((selectedUser) =>
          availableUsersList.find(
            (availableUser: { id: string }) =>
              availableUser.id === selectedUser.id,
          ),
        )
        .filter(Boolean);
      if (JSON.stringify(matchingUsers) !== JSON.stringify(engagement.users)) {
        setFormData((prev) => ({
          ...prev,
          users: matchingUsers as EngagementUser[],
        }));
      }
    }
  }, [availableUsers]);

  return (
    <>
      <div className="inset-0 flex w-1/3 flex-row items-center justify-center md:mr-4">
        <button
          type="button"
          onClick={openModal}
          className="flex items-center space-x-2 rounded-md border border-gray-400 bg-transparent px-6 py-3 text-sm font-medium text-black hover:bg-slate-100 dark:text-white dark:hover:bg-slate-600"
        >
          <span>
            <HiPencil
              className={`${isDarkMode ? "text-white" : "text-black"} h-5 w-5`}
            />
          </span>
          <span>Edit</span>
        </button>
      </div>
      <EditEngagementModal
        engagementID={engagementID}
        title={formData.title}
        wbsCode={formData.wbs_code}
        clientName={formData.client_name}
        users={formData.users}
        availableUsers={users}
        handleSubmit={handleSubmit}
        isOpen={isOpen}
        closeModal={closeModal}
        handleInputChange={handleInputChange}
        handleMultiSelectChange={handleMultiSelectChange}
      />
      <ErrorMessageModal
        isOpen={isOpenErrorModal}
        closeModal={closeErrorModal}
        errorMessage={errorMessage}
      />
    </>
  );
}
