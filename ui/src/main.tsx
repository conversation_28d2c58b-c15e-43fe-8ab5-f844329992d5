import { <PERSON><PERSON><PERSON><PERSON><PERSON>, useMsal } from "@azure/msal-react";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { ReactQueryDevtools } from "@tanstack/react-query-devtools";
import { RouterProvider, createRouter } from "@tanstack/react-router";
import Cytoscape from "cytoscape";
import cytoscapeContextMenus from "cytoscape-context-menus";
import "highlight.js/styles/atom-one-dark.css";
import { PrimeReactProvider } from "primereact/api";
// Import the shared instance
import { ProgressSpinner } from "primereact/progressspinner";
import "primereact/resources/themes/lara-light-cyan/theme.css";
import React from "react";
import ReactDOM from "react-dom/client";
import { ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

import { ThemeProvider } from "./context/ThemeProvider";
import { RolesProvider } from "./context/useRoles";
import useAuthInit from "./hooks/useAuthInit";
import "./index.css";
import { msalInstance } from "./msalInstance";
import { routeTree } from "./routeTree.gen";

const queryClient = new QueryClient();
// eslint-disable-next-line react-hooks/rules-of-hooks
Cytoscape.use(cytoscapeContextMenus as unknown as Cytoscape.Ext);

export interface MyRouterContext {
  queryClient: QueryClient;
  auth: ReturnType<typeof useMsal>;
  roles: string[];
}

export const router = createRouter({
  routeTree,
  context: {
    queryClient,
    auth: undefined!,
    roles: undefined! as string[],
  } as MyRouterContext,
});

// Register the router instance for type safety
declare module "@tanstack/react-router" {
  interface Register {
    router: typeof router;
  }
}

function InnerApp() {
  const auth = useMsal();
  return <RouterProvider router={router} context={{ auth }} />;
}

function App() {
  const { isAuthInitializing, isAuthenticated, roles } = useAuthInit();

  if (isAuthInitializing) {
    return (
      <ProgressSpinner
        style={{
          color: "#9333ea",
          justifyContent: "center",
          alignItems: "center",
          margin: "0 auto",
          display: "flex",
          minHeight: "100vh",
        }}
      />
    );
  }

  if (!isAuthenticated) {
    return (
      <ProgressSpinner
        style={{
          color: "#9333ea",
          justifyContent: "center",
          alignItems: "center",
          margin: "0 auto",
          display: "flex",
          minHeight: "100vh",
        }}
      />
    );
  }

  return (
    <MsalProvider instance={msalInstance}>
      <React.StrictMode>
        <PrimeReactProvider>
          <QueryClientProvider client={queryClient}>
            <ThemeProvider>
              <RolesProvider roles={roles}>
                <InnerApp />
              </RolesProvider>
              <ToastContainer autoClose={5000} />
              {process.env.NODE_ENV === "development" && (
                <ReactQueryDevtools initialIsOpen={false} />
              )}
            </ThemeProvider>
          </QueryClientProvider>
        </PrimeReactProvider>
      </React.StrictMode>
    </MsalProvider>
  );
}

const rootElement = document.getElementById("root")!;
if (!rootElement.innerHTML) {
  const root = ReactDOM.createRoot(rootElement);
  root.render(<App />);
}
