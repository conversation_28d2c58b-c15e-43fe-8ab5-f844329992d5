export default function decodeIdToken(idToken: string | undefined) {
  if (!idToken) {
    console.error("No ID token provided");
    return null;
  }
  try {
    const base64Url = idToken?.split(".")[1];
    if (!base64Url) {
      throw new Error("Invalid token format");
    }
    const base64 = base64Url?.replace(/-/g, "+").replace(/_/g, "/");
    if (!base64Url) {
      throw new Error("Invalid token format");
    }
    return JSON.parse(atob(base64));
  } catch (error) {
    console.error("Failed to decode ID token", error);
    return null;
  }
}
