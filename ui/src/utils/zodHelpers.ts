import { ZodError } from "zod";

/**
 * Helper function to extract the first error message from a ZodError
 * Works with both older and newer versions of Zod
 */
export function getZodErrorMessage(error: ZodError): string {
  // For newer Zod versions that use issues instead of errors
  if (error.issues && error.issues.length > 0) {
    return error.issues[0].message;
  }

  // For older Zod versions that use errors
  if (
    "errors" in error &&
    Array.isArray(error.errors) &&
    error.errors.length > 0
  ) {
    return error.errors[0].message;
  }

  // Fallback
  return error.message || "Validation error";
}
