import { HTMLProps, useEffect, useRef } from "react";

export function IndeterminateCheckbox({
  indeterminate,
  className = "checked:bg-purple-700 border-purple-700",
  ...rest
}: { indeterminate?: boolean } & HTMLProps<HTMLInputElement>) {
  const ref = useRef<HTMLInputElement>(null!);

  useEffect(() => {
    if (typeof indeterminate === "boolean") {
      ref.current.indeterminate = !rest.checked && indeterminate;
    }
  }, [ref, indeterminate, rest.checked]);

  return (
    <input
      type="checkbox"
      ref={ref}
      className={
        className + " cursor-pointer border-purple-700 checked:bg-purple-700"
      }
      {...rest}
    />
  );
}
