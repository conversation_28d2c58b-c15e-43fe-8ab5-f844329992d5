import { createFileRoute } from "@tanstack/react-router";

import ArchiveManagement from "../components/ArchiveManagement.tsx";
import ErrorPage from "../components/ErrorHandling/ErrorPage.tsx";
import NotFound from "../components/ErrorHandling/NotFound.tsx";
import { errorCode } from "../utils/assets.tsx";

export const Route = createFileRoute("/security/archive-management")({
  component: ArchiveManagement,
  errorComponent: ({ error }: any) => {
    const status = error?.status in errorCode ? error?.status : 500;
    const errorData = errorCode[status] || errorCode[500];
    return (
      <ErrorPage
        code={status}
        title={errorData.title}
        description={errorData.description}
        colour={errorData.colour}
      />
    );
  },
  notFoundComponent: () => {
    return <NotFound />;
  },
});
