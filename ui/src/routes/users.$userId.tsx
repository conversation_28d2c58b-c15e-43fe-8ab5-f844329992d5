import { useMsal } from "@azure/msal-react";
import { Disclosure, DisclosureButton } from "@headlessui/react";
import { useQueryClient, useSuspenseQuery } from "@tanstack/react-query";
import { createFileRoute } from "@tanstack/react-router";
import { AxiosError } from "axios";
import { format } from "date-fns";
import { useEffect, useRef, useState } from "react";
import { HiOutlinePencil, HiOutlineTrash } from "react-icons/hi";
import { RiAdminLine, RiUserLine } from "react-icons/ri";
import { toast } from "react-toastify";

import {
  getGetUserDetailsQueryKey,
  getGetUserDetailsQueryOptions,
  getGetUserScriptsQueryKey,
  getGetUserScriptsQueryOptions,
  getUserDetails,
  getUserScripts,
  useEditUserScript,
  useEditUserUsername,
  usePostUserScript,
  useSetUserSshKey,
} from "../client";
import { ActionButtons, ButtonProps } from "../components/ActionButtons";
import BreadCrumbs from "../components/BreadCrumbs";
import ErrorPage from "../components/ErrorHandling/ErrorPage";
import NotFound from "../components/ErrorHandling/NotFound";
import Header from "../components/Header";
import ResponsiveSideNav from "../components/ResponsiveSideNav";
import AddScriptForm from "../components/UserSettings/AddScriptForm";
import AddSshKeyForm from "../components/UserSettings/AddSshKeyForm";
import DeleteSSHKeyModal from "../components/UserSettings/DeleteSSHKeyModal";
import DeleteScriptModal from "../components/UserSettings/DeleteScriptModal";
import EditScriptForm from "../components/UserSettings/EditScriptForm";
import ViewScriptModal from "../components/UserSettings/ViewScriptModal";
import {
  CreateScriptInputBody,
  EditScriptInputBody,
  Script,
  SetSshKeyInputBody,
} from "../model";
import { errorCode } from "../utils/assets";
import { validationSchemaType } from "../utils/validationutils";
import { getZodErrorMessage } from "../utils/zodHelpers";

export const Route = createFileRoute("/users/$userId")({
  component: UserSettings,
  loader: async ({ context: { queryClient }, params }) => {
    const getEngagementDetails = queryClient.ensureQueryData(
      getGetUserDetailsQueryOptions(params.userId),
    );
    const getUserScripts = queryClient.ensureQueryData(
      getGetUserScriptsQueryOptions(params.userId),
    );

    const [userDetailsData, userScriptsData] = await Promise.all([
      getEngagementDetails,
      getUserScripts,
    ]);
    return { userDetailsData, userScriptsData };
  },
  errorComponent: ({ error }: any) => {
    const status = error?.status in errorCode ? error?.status : 500;
    const errorData = errorCode[status] || errorCode[500];
    return (
      <ErrorPage
        code={status}
        title={errorData.title}
        description={errorData.description}
        colour={errorData.colour}
      />
    );
  },
  notFoundComponent: () => {
    return <NotFound />;
  },
});

function UserSettings() {
  const { accounts } = useMsal();
  const queryClient = useQueryClient();
  const userId = accounts[0].localAccountId;

  const userQueryKey = getGetUserDetailsQueryKey(userId);
  const userQueryFn = () => getUserDetails(userId);
  const { data: userDetails } = useSuspenseQuery({
    queryKey: userQueryKey,
    queryFn: userQueryFn,
  });

  const user = userDetails?.user || [];

  const userScriptsQueryKey = getGetUserScriptsQueryKey(userId);
  const userScriptsQueryFn = () => getUserScripts(userId);
  const { data: userScriptsData } = useSuspenseQuery({
    queryKey: userScriptsQueryKey,
    queryFn: userScriptsQueryFn,
  });

  const scripts = userScriptsData.scripts || [];
  const adminScripts =
    scripts?.filter((script: Script) => script.script_type === "ADMIN") || [];
  const customScripts =
    userScriptsData.scripts?.filter(
      (script: Script) => script.script_type === "STANDARD",
    ) || [];

  const [isOpenSSHKeyModal, setIsSSHKeyModal] = useState<boolean>(false);
  const [customUsername, setCustomUsername] = useState(
    user.custom_username || "",
  );
  const [errors, setErrors] = useState({ customUsername: "" });
  const [openId, setOpenId] = useState<string | null>(null);
  const [showScriptForm, setShowScriptForm] = useState<boolean>(false);
  const [showSSHForm, setShowSSHForm] = useState<boolean>(false);
  const [isOpenDeleteModal, setIsOpenDeleteModal] = useState<boolean>(false);
  const [customScriptAction, setCustomScriptAction] = useState<string>("");
  const [selectedScript, setSelectedScript] = useState<Script | null>(null);
  const createScriptFormRef = useRef<HTMLDivElement>(null);
  const editScriptFormRef = useRef<HTMLDivElement>(null);
  const [isViewScriptModal, setIsViewScriptModal] = useState<boolean>(false);
  const [isSideNavOpen, setIsSideNavOpen] = useState(false);
  const toggleSideNav = () => setIsSideNavOpen((prev) => !prev);

  const handleInputChange =
    (field: string) => (e: { target: { value: string } }) => {
      const value = e.target.value;
      setCustomUsername(value);
      validateField(field, value);
    };

  const validateField = (field: string, value: string) => {
    const fieldSchema = validationSchemaType.customUsername;
    if (fieldSchema) {
      const validationResult = fieldSchema.safeParse(value);
      if (!validationResult.success) {
        setErrors((prevErrors) => ({
          ...prevErrors,
          [field]: getZodErrorMessage(validationResult.error),
        }));
      } else {
        setErrors((prevErrors) => ({ ...prevErrors, [field]: "" }));
      }
    }
  };

  const editScriptMutation = useEditUserScript({
    mutation: {
      onError: (error: unknown) => {
        if (error instanceof AxiosError && error.response) {
          const errorMessage =
            error.response.data?.errors?.[0]?.message ??
            "An error occurred while editing personal script.";
          toast.error(errorMessage);
        } else {
          toast.error("An unexpected error occurred.");
        }
      },
      onSettled: (_data: any, error: unknown) => {
        if (!error) {
          toast.success("Personal script has been successfully edited.");
        }
        const queryKey = getGetUserScriptsQueryKey(userId);
        queryClient.invalidateQueries({ queryKey });
        setSelectedScript(null);
      },
    },
  });

  const handleEditScript = (scriptId: string, data: EditScriptInputBody) => {
    editScriptMutation.mutate({
      userId: userId,
      scriptId: scriptId,
      data,
    });
  };

  const createScriptMutation = usePostUserScript({
    mutation: {
      onError: (error: unknown) => {
        if (error instanceof AxiosError && error.response) {
          const errorMessage =
            error.response.data?.errors?.[0]?.message ??
            "An error occurred while creating personal script";
          toast.error(errorMessage);
        } else {
          toast.error("An unexpected error occurred.");
        }
      },
      onSettled: (_data: any, error: unknown) => {
        if (!error) {
          toast.success("Personal script has been successfully created.");
        }
        const queryKey = getGetUserScriptsQueryKey(userId);
        queryClient.invalidateQueries({ queryKey });
      },
    },
  });

  const handleCreateScript = (data: CreateScriptInputBody) => {
    createScriptMutation.mutate({
      userId: userId,
      data,
    });
  };

  const setSSHKeyMutation = useSetUserSshKey({
    mutation: {
      onError: (error: unknown) => {
        if (error instanceof AxiosError && error.response) {
          const errorMessage =
            error.response.data?.errors?.[0]?.message ??
            "An error occurred while setting the SSH key";
          toast.error(errorMessage);
        } else {
          toast.error("An unexpected error occurred.");
        }
      },
      onSettled: (_data: any, error: unknown) => {
        if (!error) {
          toast.success("SSH key has been successfully set.");
        }
        const queryKey = getGetUserDetailsQueryKey(userId);
        queryClient.invalidateQueries({ queryKey });
      },
    },
  });

  const handleSetSSHKey = (data: SetSshKeyInputBody) => {
    setSSHKeyMutation.mutate({
      userId: userId,
      data,
    });
  };

  const editUserUsernameMutation = useEditUserUsername({
    mutation: {
      onError: (error: unknown) => {
        if (error instanceof AxiosError && error.response) {
          const errorMessage =
            error.response.data?.errors?.[0]?.message ?? "Name already exists.";
          toast.error(errorMessage);
        } else {
          toast.error("An unexpected error occurred.");
        }
      },
      onSettled: (_data: any, error: unknown) => {
        if (!error) {
          toast.success("Username has been successfully changed.");
        }
        const queryKey = getGetUserDetailsQueryKey(userId);
        queryClient.invalidateQueries({ queryKey });
      },
    },
  });

  const handleUpdateUsername = () => {
    editUserUsernameMutation.mutate({
      userId: userId,
      data: { custom_username: customUsername },
    });
  };

  const primaryButton: ButtonProps = {
    label: user.custom_username ? "Change Username" : "Set Username",
    onClick: handleUpdateUsername,
    variant: "primary",
    disabled: customUsername === "",
  };

  const secondarySSHButton: ButtonProps = {
    label: "Set Up SSH Key",
    onClick: () => setShowSSHForm(true),
    variant: "secondary",
    disabled: user?.ssh_key_label !== "" && user?.ssh_key !== "",
  };

  const secondaryScriptButton: ButtonProps = {
    label: "Create New Custom Script",
    onClick: () => {
      setShowScriptForm(true);
      setCustomScriptAction("");
      setTimeout(() => {
        if (createScriptFormRef.current) {
          createScriptFormRef.current.scrollIntoView({ behavior: "smooth" });
        }
      }, 100);
    },
    variant: "secondary",
  };

  useEffect(() => {
    if (editScriptFormRef.current) {
      editScriptFormRef.current.scrollIntoView({ behavior: "smooth" });
    }
  }, [selectedScript]);

  const openEditScriptForm = (selectedScriptId: string) => {
    const selectedScript =
      scripts?.find((script: Script) => script.id === selectedScriptId) || null;
    setSelectedScript(selectedScript);
    setCustomScriptAction("edit");
    setShowScriptForm(false);
  };

  const openDeleteModal = (selectedScriptId: string) => {
    const selectedScript =
      scripts?.find((script: Script) => script.id === selectedScriptId) || null;
    setSelectedScript(selectedScript);
    setCustomScriptAction("delete");
    setIsOpenDeleteModal(true);
  };

  const handleDisclosureToggle = (id: string) => {
    setOpenId(id);
    setIsViewScriptModal(true);
  };

  return (
    <div className="flex h-full w-full">
      <ResponsiveSideNav
        toggleSideNav={toggleSideNav}
        isSideNavOpen={isSideNavOpen}
      />
      <div className="flex w-full flex-col">
        <Header toggleSideNav={toggleSideNav} />
        <div className="flex h-full flex-col bg-slate-50 p-6 dark:bg-slate-800">
          <BreadCrumbs />
          <div
            id="engagements-page-settings-title"
            className="flex flex-col space-y-2 pt-4"
          >
            <span className="text-3xl font-semibold text-black dark:text-white">
              Settings
            </span>
          </div>
          <div className="container mx-auto mt-10 rounded-lg bg-white p-8 shadow-sm dark:bg-[#374357b5]">
            <div className="mb-4 text-2xl font-semibold text-black dark:text-white">
              Account Details
            </div>
            <div className="mb-8 flex flex-col space-y-4 md:flex-row md:space-y-0 md:space-x-10">
              <div className="flex flex-col">
                <label className="mb-1 flex flex-row space-x-1 dark:text-white">
                  <span className="text-gray-700 dark:text-white">Name</span>
                </label>
                <span className="font-semibold dark:text-slate-400">
                  {user.full_name}
                </span>
              </div>
              <div className="flex flex-col">
                <label className="mb-1 flex flex-row space-x-1 dark:text-white">
                  <span className="text-gray-700 dark:text-white">
                    Username
                  </span>
                </label>
                <span className="font-semibold dark:text-slate-400">
                  {user.custom_username || "?"}
                </span>
              </div>
            </div>
            <div className="flex flex-col md:flex-row">
              <div className="mb-4 flex flex-col md:mb-0">
                <input
                  className="w-full rounded-sm border border-solid border-gray-400 px-4 py-3 text-gray-700 focus:ring-2 focus:ring-purple-700 focus:outline-hidden md:w-5/6 dark:border-transparent dark:bg-slate-600 dark:text-slate-100"
                  type="text"
                  name="last_name"
                  value={customUsername}
                  onChange={handleInputChange("customUsername")}
                />
                {errors.customUsername && (
                  <p className="text-red-500">{errors.customUsername}</p>
                )}
              </div>
              <div className="items-center">
                <ActionButtons primaryButton={primaryButton} />
              </div>
            </div>
          </div>
          <div className="container mx-auto mt-10 rounded-lg bg-white p-8 shadow-sm dark:bg-[#374357b5]">
            <div className="flex flex-col justify-between space-y-2 md:flex-row md:space-y-0">
              <div className="content-center text-2xl font-semibold text-black dark:text-white">
                SSH Keys
              </div>
              <ActionButtons secondaryButton={secondarySSHButton} />
            </div>
            <div className="mt-4">
              <ul className="max-h-[calc(7*3rem)] list-none overflow-y-auto">
                <li className="sticky top-0 z-10 mb-2 flex w-full border-b-2 border-black bg-white p-2 font-bold dark:border-slate-200 dark:bg-transparent dark:text-white">
                  <div className="w-1/2 text-left">SSH Key</div>
                  <div className="w-3/8 text-left">Date Added</div>
                  <div className="w-1/8 text-left"> </div>
                </li>
                <li className="flex p-2">
                  <div className="w-1/2 text-left dark:text-slate-400">
                    {user.ssh_key_label ? user.ssh_key_label : "-"}
                  </div>
                  <div className="flex w-1/2 justify-between">
                    <div className="w-3/8 text-left dark:text-slate-400">
                      {user.ssh_key
                        ? format(
                            new Date(user?.ssh_key_creation_date),
                            "dd-MMM-yyyy, HH:mm",
                          )
                        : "-"}
                    </div>
                    <div className="w-1/8 text-left dark:text-slate-200">
                      {user?.ssh_key && (
                        <HiOutlineTrash
                          className="h-5 w-5 cursor-pointer"
                          onClick={() => setIsSSHKeyModal(true)}
                        />
                      )}
                    </div>
                  </div>
                </li>
              </ul>
            </div>
            {showSSHForm && (
              <AddSshKeyForm
                setShowSSHForm={setShowSSHForm}
                handleSetSSHKey={handleSetSSHKey}
              />
            )}
          </div>
          <div className="container mx-auto mt-10 rounded-lg bg-white p-8 shadow-sm dark:bg-[#374357b5]">
            <div className="flex flex-col justify-between space-y-2 md:flex-row md:space-y-0">
              <div className="content-center text-2xl font-semibold text-black dark:text-white">
                Custom Scripts
              </div>
              <ActionButtons secondaryButton={secondaryScriptButton} />
            </div>
            <div className="mt-4">
              <ul className="max-h-[calc(7*3rem)] list-none overflow-y-auto">
                <li className="sticky top-0 z-10 mb-2 flex w-full border-b-2 border-black bg-white p-2 font-bold dark:border-slate-200 dark:bg-[#303c4f] dark:text-white">
                  <div className="w-full text-left sm:w-1/4">Name</div>
                  <div className="w-full text-left sm:w-1/2">Description</div>
                  <div className="w-full text-left sm:w-1/4">Creation Date</div>
                </li>
                {customScripts.length === 0 && (
                  <div className="p-2">No custom scripts available.</div>
                )}
                <Disclosure as="div">
                  {customScripts &&
                    customScripts
                      ?.slice()
                      .sort(
                        (
                          a: { created_at: string },
                          b: { created_at: string },
                        ) =>
                          new Date(a.created_at).getTime() -
                          new Date(b.created_at).getTime(),
                      )
                      .map((script: Script) => (
                        <div key={script.id}>
                          <li className="flex p-2">
                            <div
                              className={`w-full md:w-1/4 ${script.script_type === "ADMIN" ? "text-sky-500 dark:text-sky-300" : "text-purple-500 dark:text-purple-300"}`}
                            >
                              <DisclosureButton
                                onClick={() =>
                                  handleDisclosureToggle(script.id)
                                }
                                className="flex cursor-pointer text-left"
                                as="div"
                              >
                                <RiUserLine className="mr-4 h-5 w-5 text-purple-600 dark:text-purple-300" />
                                {script.name || "-"}
                              </DisclosureButton>
                            </div>
                            <div className="w-full text-left md:w-1/2 dark:text-slate-400">
                              {script.description &&
                              script.description.length > 100
                                ? `${script.description.slice(0, 50)}...`
                                : script.description || "-"}
                            </div>
                            <div className="flex w-full justify-between md:w-1/4">
                              <div className="text-left dark:text-slate-400">
                                {format(
                                  new Date(script.created_at),
                                  "dd-MMM-yyyy, HH:mm",
                                )}
                              </div>
                              <div className="flex space-x-2 text-left dark:text-slate-200">
                                <HiOutlinePencil
                                  className="h-5 w-5 cursor-pointer"
                                  onClick={() => {
                                    openEditScriptForm(script.id);
                                  }}
                                />
                                <HiOutlineTrash
                                  className="h-5 w-5 cursor-pointer"
                                  onClick={() => openDeleteModal(script.id)}
                                />
                              </div>
                            </div>
                          </li>
                          {openId === script.id && (
                            <ViewScriptModal
                              isOpen={isViewScriptModal}
                              closeModal={() => setIsViewScriptModal(false)}
                              scriptDetails={script}
                            />
                          )}
                        </div>
                      ))}
                </Disclosure>
              </ul>
            </div>
            {showScriptForm && (
              <div ref={createScriptFormRef}>
                <AddScriptForm
                  setShowScriptForm={setShowScriptForm}
                  handleCreateScript={handleCreateScript}
                />
              </div>
            )}
            {customScriptAction === "edit" && selectedScript && (
              <div ref={editScriptFormRef}>
                <EditScriptForm
                  setShowScriptForm={() => setCustomScriptAction("")}
                  handleEditScript={handleEditScript}
                  script={selectedScript}
                  isOpen={
                    customScriptAction === "edit" && selectedScript
                      ? true
                      : false
                  }
                  closeModal={() => setCustomScriptAction("")}
                />
              </div>
            )}
          </div>
          <div className="container mx-auto mt-10 rounded-lg bg-white p-8 shadow-sm dark:bg-[#374357b5]">
            <div className="flex flex-col justify-between space-y-2 md:flex-row md:space-y-0">
              <div className="content-center text-2xl font-semibold text-black dark:text-white">
                Admin Scripts
              </div>
            </div>
            <div className="mt-4">
              <ul className="max-h-[calc(7*3rem)] list-none overflow-y-auto">
                <li className="sticky top-0 z-10 mb-2 flex w-full border-b-2 border-black bg-white p-2 font-bold dark:border-slate-200 dark:bg-[#303c4f] dark:text-white">
                  <div className="w-full text-left sm:w-1/4">Name</div>
                  <div className="w-full text-left sm:w-1/2">Description</div>
                  <div className="w-full text-left sm:w-1/4">Creation Date</div>
                </li>

                <Disclosure as="div">
                  {adminScripts
                    ? adminScripts
                        ?.slice()
                        .sort(
                          (
                            a: { created_at: string },
                            b: { created_at: string },
                          ) =>
                            new Date(a.created_at).getTime() -
                            new Date(b.created_at).getTime(),
                        )
                        .map((script: Script) => (
                          <div key={script.id}>
                            <li className="flex p-2">
                              <div
                                className={`w-full md:w-1/4 ${script.script_type === "ADMIN" ? "text-sky-500 dark:text-sky-300" : "text-purple-500 dark:text-purple-300"}`}
                              >
                                <DisclosureButton
                                  onClick={() =>
                                    handleDisclosureToggle(script.id)
                                  }
                                  className="flex cursor-pointer text-left"
                                  as="div"
                                >
                                  <RiAdminLine className="mr-4 h-5 w-5 text-sky-600 dark:text-sky-300" />
                                  {script.name || "-"}
                                </DisclosureButton>
                              </div>
                              <div className="w-full text-left md:w-1/2 dark:text-slate-400">
                                {script.description &&
                                script.description.length > 100
                                  ? `${script.description.slice(0, 50)}...`
                                  : script.description || "-"}
                              </div>
                              <div className="flex w-full justify-between md:w-1/4">
                                <div className="text-left dark:text-slate-400">
                                  {format(
                                    new Date(script.created_at),
                                    "dd-MMM-yyyy, HH:mm",
                                  )}
                                </div>
                                <div className="flex space-x-2 text-left dark:text-slate-200">
                                  <div className="h-5 w-10" />
                                </div>
                              </div>
                            </li>
                            {openId === script.id && (
                              <ViewScriptModal
                                isOpen={isViewScriptModal}
                                closeModal={() => setIsViewScriptModal(false)}
                                scriptDetails={script}
                              />
                            )}
                          </div>
                        ))
                    : null}
                </Disclosure>
              </ul>
            </div>
          </div>
          {customScriptAction === "delete" && selectedScript && (
            <DeleteScriptModal
              userId={userId}
              isOpen={isOpenDeleteModal}
              closeModal={() => setIsOpenDeleteModal(false)}
              script={selectedScript}
            />
          )}
        </div>
        <DeleteSSHKeyModal
          isOpen={isOpenSSHKeyModal}
          closeModal={() => setIsSSHKeyModal(false)}
          userId={userId}
        />
      </div>
    </div>
  );
}
