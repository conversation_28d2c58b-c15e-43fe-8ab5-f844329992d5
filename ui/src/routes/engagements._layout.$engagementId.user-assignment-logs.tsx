import { useSuspenseQuery } from "@tanstack/react-query";
import { Link, createFileRoute } from "@tanstack/react-router";

import {
  getEngagement,
  getGetEngagementQueryKey,
  getGetEngagementQueryOptions,
  getGetLogAssignmentsEngagementIdQueryKey,
  getGetLogAssignmentsEngagementIdQueryOptions,
  useGetLogAssignmentsEngagementId,
} from "../client";
import EngagementInfo from "../components/EngagementInfo";
import ErrorPage from "../components/ErrorHandling/ErrorPage";
import NotFound from "../components/ErrorHandling/NotFound";
import { LogsColumn } from "../components/LogAssignments/LogAssignmentsColumn";
import Table from "../components/Table";
import { errorCode } from "../utils/assets";

export const Route = createFileRoute(
  "/engagements/_layout/$engagementId/user-assignment-logs",
)({
  errorComponent: ({ error }: any) => {
    const status = error?.status in errorCode ? error?.status : 500;
    const errorData = errorCode[status] || errorCode[500];
    return (
      <ErrorPage
        code={status}
        title={errorData.title}
        description={errorData.description}
        colour={errorData.colour}
      />
    );
  },
  notFoundComponent: () => {
    return <NotFound />;
  },
  component: LogsComponent,
  loader: async ({ context: { queryClient }, params }) => {
    const getEngagementDetails = queryClient.ensureQueryData(
      getGetEngagementQueryOptions(params.engagementId),
    );
    const getLogAssignments = queryClient.ensureQueryData(
      getGetLogAssignmentsEngagementIdQueryOptions(params.engagementId),
    );
    const [engagementData, logsData] = await Promise.all([
      getEngagementDetails,
      getLogAssignments,
    ]);
    return { engagementData, logsData };
  },
});

function LogsComponent() {
  const { engagementId } = Route.useParams();
  const { data: assignmentLogs } = useGetLogAssignmentsEngagementId(
    engagementId,
    {
      query: {
        queryKey: getGetLogAssignmentsEngagementIdQueryKey(engagementId),
      },
    },
  );

  const logs = assignmentLogs?.userAssignmentLogs || [];
  const engagementQueryKey = getGetEngagementQueryKey(engagementId);
  const queryFn = () => getEngagement(engagementId);
  const { data: engagementList } = useSuspenseQuery({
    queryKey: engagementQueryKey,
    queryFn,
  });
  const engagement = engagementList?.engagement || [];

  return (
    <>
      <div
        id="engagements-page-main-title"
        className="flex flex-row justify-between pt-4"
      >
        <div className="text-3xl font-semibold text-black dark:text-white">
          {engagement.title} - User Assignment Logs
        </div>
        <div className="hidden md:flex">
          <Link
            to={`/engagements/$engagementId`}
            params={{ engagementId: engagement.id }}
          >
            <span className="text-indigo-600 underline underline-offset-2 hover:text-indigo-400 dark:text-indigo-300 dark:hover:text-indigo-200">
              Engagement Details
            </span>
          </Link>
        </div>
      </div>
      <EngagementInfo engagement={engagement} />
      <div id="logs-list" className="relative mt-9 flex flex-col space-y-2">
        <div className="flex w-full flex-col rounded-lg bg-white px-3 py-3 dark:bg-[#374357b5]">
          <div className="w-full overflow-hidden">
            <div className="h-8" />
            <Table data={logs} columns={LogsColumn} />
          </div>
        </div>
      </div>
    </>
  );
}
