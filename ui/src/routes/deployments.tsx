import { Outlet, createFileRoute, useMatchRoute } from "@tanstack/react-router";
import { useState } from "react";

import { getGetDeploymentsQueryOptions } from "../client";
import BreadCrumbs from "../components/BreadCrumbs";
import ErrorPage from "../components/ErrorHandling/ErrorPage";
import NotFound from "../components/ErrorHandling/NotFound";
import Header from "../components/Header";
import ResponsiveSideNav from "../components/ResponsiveSideNav";
import { errorCode } from "../utils/assets";

export const Route = createFileRoute("/deployments")({
  component: Deployments,
  loader: async ({ context: { queryClient } }) => {
    return queryClient.ensureQueryData(getGetDeploymentsQueryOptions());
  },
  errorComponent: ({ error }: any) => {
    const status = error?.status in errorCode ? error?.status : 500;
    const errorData = errorCode[status] || errorCode[500];
    return (
      <ErrorPage
        code={status}
        title={errorData.title}
        description={errorData.description}
        colour={errorData.colour}
      />
    );
  },
  notFoundComponent: () => {
    return <NotFound />;
  },
});

function Deployments() {
  const matchRoute = useMatchRoute();
  const deploymentDetailsMatch = matchRoute({
    to: "/deployments/$deploymentId",
  });

  const [isSideNavOpen, setIsSideNavOpen] = useState(false);
  const toggleSideNav = () => setIsSideNavOpen((prev) => !prev);

  return (
    <div className="flex h-full w-full">
      <ResponsiveSideNav
        toggleSideNav={toggleSideNav}
        isSideNavOpen={isSideNavOpen}
      />
      <div className="flex w-full flex-col">
        <Header toggleSideNav={toggleSideNav} />
        <div className="flex h-full flex-col bg-slate-50 px-3 py-6 md:px-6 lg:px-8 dark:bg-slate-800">
          <div className={deploymentDetailsMatch ? "max-w-6xl" : "w-full"}>
            <BreadCrumbs />
            <div
              id="deployments-page-main-title"
              className="flex flex-col space-y-3 pt-6 pb-2"
            >
              <span className="text-3xl font-semibold text-black dark:text-white">
                {deploymentDetailsMatch &&
                Object.keys(deploymentDetailsMatch).length > 0
                  ? "Deployment Details"
                  : "Deployments"}
              </span>
              {!deploymentDetailsMatch && (
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  View and manage your deployments
                </p>
              )}
            </div>
            <div className={!deploymentDetailsMatch ? "-mt-4" : ""}>
              <Outlet />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
