/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { createFileRoute } from '@tanstack/react-router'

import { Route as rootRouteImport } from './routes/__root'
import { Route as InventoryRouteImport } from './routes/inventory'
import { Route as DomainsRouteImport } from './routes/domains'
import { Route as DeploymentsRouteImport } from './routes/deployments'
import { Route as IndexRouteImport } from './routes/index'
import { Route as DeploymentsIndexRouteImport } from './routes/deployments.index'
import { Route as UsersUserIdRouteImport } from './routes/users.$userId'
import { Route as SecurityUserManagementRouteImport } from './routes/security.user-management'
import { Route as SecuritySetInstanceTypesRouteImport } from './routes/security.set-instance-types'
import { Route as SecurityScriptManagementRouteImport } from './routes/security.script-management'
import { Route as SecurityAssignmentLogsRouteImport } from './routes/security.assignment-logs'
import { Route as SecurityArchiveManagementRouteImport } from './routes/security.archive-management'
import { Route as EngagementsLayoutRouteImport } from './routes/engagements._layout'
import { Route as DeploymentsDeploymentIdRouteImport } from './routes/deployments.$deploymentId'
import { Route as EngagementsLayoutEngagementIdIndexRouteImport } from './routes/engagements._layout.$engagementId.index'
import { Route as EngagementsLayoutEngagementIdUserAssignmentLogsRouteImport } from './routes/engagements._layout.$engagementId.user-assignment-logs'

const EngagementsRouteImport = createFileRoute('/engagements')()

const EngagementsRoute = EngagementsRouteImport.update({
  id: '/engagements',
  path: '/engagements',
  getParentRoute: () => rootRouteImport,
} as any)
const InventoryRoute = InventoryRouteImport.update({
  id: '/inventory',
  path: '/inventory',
  getParentRoute: () => rootRouteImport,
} as any)
const DomainsRoute = DomainsRouteImport.update({
  id: '/domains',
  path: '/domains',
  getParentRoute: () => rootRouteImport,
} as any)
const DeploymentsRoute = DeploymentsRouteImport.update({
  id: '/deployments',
  path: '/deployments',
  getParentRoute: () => rootRouteImport,
} as any)
const IndexRoute = IndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRouteImport,
} as any)
const DeploymentsIndexRoute = DeploymentsIndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => DeploymentsRoute,
} as any)
const UsersUserIdRoute = UsersUserIdRouteImport.update({
  id: '/users/$userId',
  path: '/users/$userId',
  getParentRoute: () => rootRouteImport,
} as any)
const SecurityUserManagementRoute = SecurityUserManagementRouteImport.update({
  id: '/security/user-management',
  path: '/security/user-management',
  getParentRoute: () => rootRouteImport,
} as any)
const SecuritySetInstanceTypesRoute =
  SecuritySetInstanceTypesRouteImport.update({
    id: '/security/set-instance-types',
    path: '/security/set-instance-types',
    getParentRoute: () => rootRouteImport,
  } as any)
const SecurityScriptManagementRoute =
  SecurityScriptManagementRouteImport.update({
    id: '/security/script-management',
    path: '/security/script-management',
    getParentRoute: () => rootRouteImport,
  } as any)
const SecurityAssignmentLogsRoute = SecurityAssignmentLogsRouteImport.update({
  id: '/security/assignment-logs',
  path: '/security/assignment-logs',
  getParentRoute: () => rootRouteImport,
} as any)
const SecurityArchiveManagementRoute =
  SecurityArchiveManagementRouteImport.update({
    id: '/security/archive-management',
    path: '/security/archive-management',
    getParentRoute: () => rootRouteImport,
  } as any)
const EngagementsLayoutRoute = EngagementsLayoutRouteImport.update({
  id: '/_layout',
  getParentRoute: () => EngagementsRoute,
} as any)
const DeploymentsDeploymentIdRoute = DeploymentsDeploymentIdRouteImport.update({
  id: '/$deploymentId',
  path: '/$deploymentId',
  getParentRoute: () => DeploymentsRoute,
} as any)
const EngagementsLayoutEngagementIdIndexRoute =
  EngagementsLayoutEngagementIdIndexRouteImport.update({
    id: '/$engagementId/',
    path: '/$engagementId/',
    getParentRoute: () => EngagementsLayoutRoute,
  } as any)
const EngagementsLayoutEngagementIdUserAssignmentLogsRoute =
  EngagementsLayoutEngagementIdUserAssignmentLogsRouteImport.update({
    id: '/$engagementId/user-assignment-logs',
    path: '/$engagementId/user-assignment-logs',
    getParentRoute: () => EngagementsLayoutRoute,
  } as any)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/deployments': typeof DeploymentsRouteWithChildren
  '/domains': typeof DomainsRoute
  '/inventory': typeof InventoryRoute
  '/deployments/$deploymentId': typeof DeploymentsDeploymentIdRoute
  '/engagements': typeof EngagementsLayoutRouteWithChildren
  '/security/archive-management': typeof SecurityArchiveManagementRoute
  '/security/assignment-logs': typeof SecurityAssignmentLogsRoute
  '/security/script-management': typeof SecurityScriptManagementRoute
  '/security/set-instance-types': typeof SecuritySetInstanceTypesRoute
  '/security/user-management': typeof SecurityUserManagementRoute
  '/users/$userId': typeof UsersUserIdRoute
  '/deployments/': typeof DeploymentsIndexRoute
  '/engagements/$engagementId/user-assignment-logs': typeof EngagementsLayoutEngagementIdUserAssignmentLogsRoute
  '/engagements/$engagementId': typeof EngagementsLayoutEngagementIdIndexRoute
}
export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/domains': typeof DomainsRoute
  '/inventory': typeof InventoryRoute
  '/deployments/$deploymentId': typeof DeploymentsDeploymentIdRoute
  '/engagements': typeof EngagementsLayoutRouteWithChildren
  '/security/archive-management': typeof SecurityArchiveManagementRoute
  '/security/assignment-logs': typeof SecurityAssignmentLogsRoute
  '/security/script-management': typeof SecurityScriptManagementRoute
  '/security/set-instance-types': typeof SecuritySetInstanceTypesRoute
  '/security/user-management': typeof SecurityUserManagementRoute
  '/users/$userId': typeof UsersUserIdRoute
  '/deployments': typeof DeploymentsIndexRoute
  '/engagements/$engagementId/user-assignment-logs': typeof EngagementsLayoutEngagementIdUserAssignmentLogsRoute
  '/engagements/$engagementId': typeof EngagementsLayoutEngagementIdIndexRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/': typeof IndexRoute
  '/deployments': typeof DeploymentsRouteWithChildren
  '/domains': typeof DomainsRoute
  '/inventory': typeof InventoryRoute
  '/deployments/$deploymentId': typeof DeploymentsDeploymentIdRoute
  '/engagements': typeof EngagementsRouteWithChildren
  '/engagements/_layout': typeof EngagementsLayoutRouteWithChildren
  '/security/archive-management': typeof SecurityArchiveManagementRoute
  '/security/assignment-logs': typeof SecurityAssignmentLogsRoute
  '/security/script-management': typeof SecurityScriptManagementRoute
  '/security/set-instance-types': typeof SecuritySetInstanceTypesRoute
  '/security/user-management': typeof SecurityUserManagementRoute
  '/users/$userId': typeof UsersUserIdRoute
  '/deployments/': typeof DeploymentsIndexRoute
  '/engagements/_layout/$engagementId/user-assignment-logs': typeof EngagementsLayoutEngagementIdUserAssignmentLogsRoute
  '/engagements/_layout/$engagementId/': typeof EngagementsLayoutEngagementIdIndexRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/deployments'
    | '/domains'
    | '/inventory'
    | '/deployments/$deploymentId'
    | '/engagements'
    | '/security/archive-management'
    | '/security/assignment-logs'
    | '/security/script-management'
    | '/security/set-instance-types'
    | '/security/user-management'
    | '/users/$userId'
    | '/deployments/'
    | '/engagements/$engagementId/user-assignment-logs'
    | '/engagements/$engagementId'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | '/domains'
    | '/inventory'
    | '/deployments/$deploymentId'
    | '/engagements'
    | '/security/archive-management'
    | '/security/assignment-logs'
    | '/security/script-management'
    | '/security/set-instance-types'
    | '/security/user-management'
    | '/users/$userId'
    | '/deployments'
    | '/engagements/$engagementId/user-assignment-logs'
    | '/engagements/$engagementId'
  id:
    | '__root__'
    | '/'
    | '/deployments'
    | '/domains'
    | '/inventory'
    | '/deployments/$deploymentId'
    | '/engagements'
    | '/engagements/_layout'
    | '/security/archive-management'
    | '/security/assignment-logs'
    | '/security/script-management'
    | '/security/set-instance-types'
    | '/security/user-management'
    | '/users/$userId'
    | '/deployments/'
    | '/engagements/_layout/$engagementId/user-assignment-logs'
    | '/engagements/_layout/$engagementId/'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  DeploymentsRoute: typeof DeploymentsRouteWithChildren
  DomainsRoute: typeof DomainsRoute
  InventoryRoute: typeof InventoryRoute
  EngagementsRoute: typeof EngagementsRouteWithChildren
  SecurityArchiveManagementRoute: typeof SecurityArchiveManagementRoute
  SecurityAssignmentLogsRoute: typeof SecurityAssignmentLogsRoute
  SecurityScriptManagementRoute: typeof SecurityScriptManagementRoute
  SecuritySetInstanceTypesRoute: typeof SecuritySetInstanceTypesRoute
  SecurityUserManagementRoute: typeof SecurityUserManagementRoute
  UsersUserIdRoute: typeof UsersUserIdRoute
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/engagements': {
      id: '/engagements'
      path: '/engagements'
      fullPath: '/engagements'
      preLoaderRoute: typeof EngagementsRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/inventory': {
      id: '/inventory'
      path: '/inventory'
      fullPath: '/inventory'
      preLoaderRoute: typeof InventoryRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/domains': {
      id: '/domains'
      path: '/domains'
      fullPath: '/domains'
      preLoaderRoute: typeof DomainsRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/deployments': {
      id: '/deployments'
      path: '/deployments'
      fullPath: '/deployments'
      preLoaderRoute: typeof DeploymentsRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/deployments/': {
      id: '/deployments/'
      path: '/'
      fullPath: '/deployments/'
      preLoaderRoute: typeof DeploymentsIndexRouteImport
      parentRoute: typeof DeploymentsRoute
    }
    '/users/$userId': {
      id: '/users/$userId'
      path: '/users/$userId'
      fullPath: '/users/$userId'
      preLoaderRoute: typeof UsersUserIdRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/security/user-management': {
      id: '/security/user-management'
      path: '/security/user-management'
      fullPath: '/security/user-management'
      preLoaderRoute: typeof SecurityUserManagementRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/security/set-instance-types': {
      id: '/security/set-instance-types'
      path: '/security/set-instance-types'
      fullPath: '/security/set-instance-types'
      preLoaderRoute: typeof SecuritySetInstanceTypesRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/security/script-management': {
      id: '/security/script-management'
      path: '/security/script-management'
      fullPath: '/security/script-management'
      preLoaderRoute: typeof SecurityScriptManagementRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/security/assignment-logs': {
      id: '/security/assignment-logs'
      path: '/security/assignment-logs'
      fullPath: '/security/assignment-logs'
      preLoaderRoute: typeof SecurityAssignmentLogsRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/security/archive-management': {
      id: '/security/archive-management'
      path: '/security/archive-management'
      fullPath: '/security/archive-management'
      preLoaderRoute: typeof SecurityArchiveManagementRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/engagements/_layout': {
      id: '/engagements/_layout'
      path: '/engagements'
      fullPath: '/engagements'
      preLoaderRoute: typeof EngagementsLayoutRouteImport
      parentRoute: typeof EngagementsRoute
    }
    '/deployments/$deploymentId': {
      id: '/deployments/$deploymentId'
      path: '/$deploymentId'
      fullPath: '/deployments/$deploymentId'
      preLoaderRoute: typeof DeploymentsDeploymentIdRouteImport
      parentRoute: typeof DeploymentsRoute
    }
    '/engagements/_layout/$engagementId/': {
      id: '/engagements/_layout/$engagementId/'
      path: '/$engagementId'
      fullPath: '/engagements/$engagementId'
      preLoaderRoute: typeof EngagementsLayoutEngagementIdIndexRouteImport
      parentRoute: typeof EngagementsLayoutRoute
    }
    '/engagements/_layout/$engagementId/user-assignment-logs': {
      id: '/engagements/_layout/$engagementId/user-assignment-logs'
      path: '/$engagementId/user-assignment-logs'
      fullPath: '/engagements/$engagementId/user-assignment-logs'
      preLoaderRoute: typeof EngagementsLayoutEngagementIdUserAssignmentLogsRouteImport
      parentRoute: typeof EngagementsLayoutRoute
    }
  }
}

interface DeploymentsRouteChildren {
  DeploymentsDeploymentIdRoute: typeof DeploymentsDeploymentIdRoute
  DeploymentsIndexRoute: typeof DeploymentsIndexRoute
}

const DeploymentsRouteChildren: DeploymentsRouteChildren = {
  DeploymentsDeploymentIdRoute: DeploymentsDeploymentIdRoute,
  DeploymentsIndexRoute: DeploymentsIndexRoute,
}

const DeploymentsRouteWithChildren = DeploymentsRoute._addFileChildren(
  DeploymentsRouteChildren,
)

interface EngagementsLayoutRouteChildren {
  EngagementsLayoutEngagementIdUserAssignmentLogsRoute: typeof EngagementsLayoutEngagementIdUserAssignmentLogsRoute
  EngagementsLayoutEngagementIdIndexRoute: typeof EngagementsLayoutEngagementIdIndexRoute
}

const EngagementsLayoutRouteChildren: EngagementsLayoutRouteChildren = {
  EngagementsLayoutEngagementIdUserAssignmentLogsRoute:
    EngagementsLayoutEngagementIdUserAssignmentLogsRoute,
  EngagementsLayoutEngagementIdIndexRoute:
    EngagementsLayoutEngagementIdIndexRoute,
}

const EngagementsLayoutRouteWithChildren =
  EngagementsLayoutRoute._addFileChildren(EngagementsLayoutRouteChildren)

interface EngagementsRouteChildren {
  EngagementsLayoutRoute: typeof EngagementsLayoutRouteWithChildren
}

const EngagementsRouteChildren: EngagementsRouteChildren = {
  EngagementsLayoutRoute: EngagementsLayoutRouteWithChildren,
}

const EngagementsRouteWithChildren = EngagementsRoute._addFileChildren(
  EngagementsRouteChildren,
)

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  DeploymentsRoute: DeploymentsRouteWithChildren,
  DomainsRoute: DomainsRoute,
  InventoryRoute: InventoryRoute,
  EngagementsRoute: EngagementsRouteWithChildren,
  SecurityArchiveManagementRoute: SecurityArchiveManagementRoute,
  SecurityAssignmentLogsRoute: SecurityAssignmentLogsRoute,
  SecurityScriptManagementRoute: SecurityScriptManagementRoute,
  SecuritySetInstanceTypesRoute: SecuritySetInstanceTypesRoute,
  SecurityUserManagementRoute: SecurityUserManagementRoute,
  UsersUserIdRoute: UsersUserIdRoute,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
