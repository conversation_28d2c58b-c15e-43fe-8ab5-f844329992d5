import Axios, { AxiosError, AxiosRequestConfig } from "axios";

import { acquireToken } from "./acquireToken.ts";

// Use the environment variable for the backend URL
const backendUrl = import.meta.env.VITE_BASE_URL_API;

if (!backendUrl) {
  throw new Error(
    "VITE_BACKEND_URL is not defined in the environment variables.",
  );
}

// Create a pre-configured Axios instance with the base URL from .env
export const AXIOS_INSTANCE = Axios.create({
  baseURL: backendUrl,
});

// Define the custom instance function with token acquisition and cancellation support
export const customInstance = async <T>(
  config: AxiosRequestConfig,
  options?: AxiosRequestConfig,
): Promise<T> => {
  // Acquire the access token for authorization
  const accessToken = await acquireToken();

  // Set up cancellation for the request
  const source = Axios.CancelToken.source();

  // Merge the passed config with additional settings (authorization headers, options, and cancellation)
  const axiosConfig: AxiosRequestConfig = {
    ...config,
    ...options,
    headers: {
      ...config.headers,
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`,
    },
    cancelToken: source.token,
  };

  // Send the request and extract `data` from the response
  const promise = AXIOS_INSTANCE(axiosConfig).then(({ data }) => data as T);

  // Add a `cancel` method to the promise for canceling the request if needed
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-ignore
  promise.cancel = () => {
    source.cancel("Query was cancelled");
  };

  return promise;
};

// Custom error type to extend AxiosError (optional)
export type ErrorType<Error> = AxiosError<Error>;

// Optional custom body type to transform data format if needed
export type BodyType<BodyData> = BodyData;
