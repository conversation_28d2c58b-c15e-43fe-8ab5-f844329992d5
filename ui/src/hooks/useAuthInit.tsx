import {
  AuthenticationResult,
  EventMessage,
  EventType,
} from "@azure/msal-browser";
import { useMsal } from "@azure/msal-react";
import { useEffect, useState } from "react";

import { loginRequest } from "../authConfig";
import { msalInstance } from "../msalInstance";

function useAuthInit() {
  const [isAuthInitializing, setIsAuthInitializing] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [roles, setRoles] = useState<string[]>([]);
  const { instance, inProgress } = useMsal();

  useEffect(() => {
    const initializeAuth = async () => {
      try {
        // Initialize MSAL
        await msalInstance.initialize();
        await msalInstance.handleRedirectPromise();

        const accounts = msalInstance.getAllAccounts();
        let activeAccount = msalInstance.getActiveAccount();

        if (!activeAccount) {
          if (accounts.length === 1) {
            // If there is only one account, use it
            msalInstance.setActiveAccount(accounts[0]);
            activeAccount = accounts[0];
          } else if (accounts.length > 1) {
            // If multiple accounts exist, prompt user to log in explicitly
            await msalInstance.loginRedirect(loginRequest);
          } else {
            // If no accounts exist, prompt user to log in
            await msalInstance.loginRedirect(loginRequest);
          }
        }

        // Set authentication state and decode roles if an account is active
        if (activeAccount) {
          setIsAuthenticated(true);

          const idToken = activeAccount.idTokenClaims as { roles?: string[] };
          if (idToken?.roles) {
            setRoles(idToken.roles);
          }
        }
      } catch (error) {
        console.error("Error during MSAL initialization:", error);
      }

      // Handle login success events
      const callbackId = msalInstance.addEventCallback(
        (event: EventMessage) => {
          if (event.eventType === EventType.LOGIN_SUCCESS && event.payload) {
            const payload = event.payload as AuthenticationResult;

            // Explicitly set the active account after login
            msalInstance.setActiveAccount(payload.account);
            setIsAuthenticated(true);

            // Decode roles after login
            const idToken = payload.account.idTokenClaims as {
              roles?: string[];
            };
            if (idToken?.roles) {
              setRoles(idToken.roles);
            }
          }
        },
      );

      setIsAuthInitializing(false);

      return () => {
        if (callbackId) msalInstance.removeEventCallback(callbackId);
      };
    };

    initializeAuth();
  }, [instance, inProgress]);

  return { isAuthInitializing, isAuthenticated, roles };
}

export default useAuthInit;
