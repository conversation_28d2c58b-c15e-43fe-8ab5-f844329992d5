# engage-v2-ui

<!-- TOC -->

- [engage-v2-ui](#engage-v2-ui)
  - [Prerequisites](#prerequisites)
    - [1. Install Node.js 22 and pnpm](#1-install-nodejs-22-and-pnpm)
      - [MacOS (using brew)](#macos-using-brew)
      - [Linux](#linux)
    - [2. Create a `.env` file in the root of the project with the necessary variables](#2-create-a-env-file-in-the-root-of-the-project-with-the-necessary-variables)
    - [3. Install dependencies](#3-install-dependencies)
  - [Run development server](#run-development-server)
  - [Build production bundle](#build-production-bundle)
  - [Lint all code](#lint-all-code)
  - [Format all code using Prettier](#format-all-code-using-prettier)

<!-- TOC -->

Engage UI based on [Vite](https://vitejs.dev/) using
the [React + TypeScript + Vite](https://github.com/vitejs/vite/tree/main/packages/create-vite/template-react-ts)
template, and [TanStack Router](https://tanstack.com/router/latest) for client-side routing.

## Prerequisites

### 1. Install [Node.js 22](https://nodejs.org/en/download) and [pnpm](https://pnpm.io/installation)

#### MacOS (using [brew](https://brew.sh/))

```bash
brew install node@22 pnpm
```

#### Linux

```bash
# installs nvm (Node Version Manager)
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.40.1/install.sh | bash
# download and install Node.js
nvm install 22
# download and install pnpm
curl -fsSL https://get.pnpm.io/install.sh | env PNPM_VERSION=10.0.0 sh -
```

### 2. Create a `.env` file in the root of the project with the necessary variables

```dotenv
VITE_AZURE_CLIENT_ID=...
VITE_AZURE_AUTHORITY=...
VITE_AZURE_API_SCOPE=api://.../user_impersonation
VITE_POST_LOGOUT_REDIRECT_URI=http://localhost:5173

# Enage API base URL
VITE_BASE_URL_API=http://localhost:8000
```

### 3. Install dependencies

```bash
pnpm install
```

## Run development server

```bash
pnpm run dev
```

The UI will run by default on http://localhost:5173

## Build production bundle

```bash
pnpm run build
```

## Lint all code

```bash
pnpm run lint
```

## Format all code using [Prettier](https://prettier.io/)

```bash
pnpm run format
```

## Orval

### Steps

1. Navigate to `http://localhost:8080/docs#/`.
2. Export the yaml file => Go to `Overview -> Export -> Original` and replace the `ui/document.yaml` file.
3. Delete the `src/model` folder and the `src/client.ts` file.
4. Run:
   ```bash
   pnpm run orval
